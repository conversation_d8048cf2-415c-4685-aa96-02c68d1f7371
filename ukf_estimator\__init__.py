"""
基于无迹卡尔曼滤波(UKF)的电池状态估算系统

本模块实现了基于UKF的SOC、欧姆内阻和极化内阻估算功能，
与项目中的虚拟电池系统(battery_simulator)紧密集成。

主要功能:
- SOC实时估算
- 欧姆内阻(R0)估算  
- 极化内阻(R1)和极化电容(C1)估算
- 参数在线辨识
- 估算精度验证

核心组件:
- UKFCore: 无迹卡尔曼滤波核心算法
- BatteryModel: 电池等效电路模型
- StateEstimator: 状态估算器主类
- DataInterface: 与虚拟电池系统的数据接口
"""

from .ukf_core import UKFCore, UKFConfig, UKFException
from .battery_models import FirstOrderRCModel, BatteryParameters
from .state_estimator import BatteryStateEstimator, EstimationResult
from .data_interface import VirtualBatteryInterface, DataBuffer
from .parameter_identification import ParameterIdentifier
from .validation import EstimationValidator
from .visualization import EstimationVisualizer

__version__ = "1.0.0"
__author__ = "Battery Anomaly Detection System v3"

# 导出主要类和函数
__all__ = [
    # 核心类
    'UKFCore',
    'UKFConfig',
    'UKFException',
    'FirstOrderRCModel',
    'BatteryParameters',
    'BatteryStateEstimator',
    'EstimationResult',

    # 接口和工具
    'VirtualBatteryInterface',
    'DataBuffer',
    'ParameterIdentifier',
    'EstimationValidator',
    'EstimationVisualizer',
]

# 默认配置 - 优化的参数以提高准确性
DEFAULT_UKF_CONFIG = UKFConfig(
    alpha=1e-3,      # 更小的alpha值，提高数值稳定性
    beta=2.0,        # UKF参数β，用于高阶矩匹配
    kappa=0.0,       # 标准设置
    process_noise_std={
        'soc': 5e-6,     # 进一步减小SOC过程噪声
        'v1': 5e-5,      # 减小极化电压过程噪声
        'r0': 1e-6,      # 适中的欧姆内阻过程噪声，允许参数调整
        'r1': 1e-6,      # 适中的极化内阻过程噪声
        'c1': 1e-6,      # 减小极化电容过程噪声
    },
    measurement_noise_std=10e-3, # 适中的测量噪声(10mV)
    thread_safe=True,            # 启用线程安全
)

# 默认电池参数
DEFAULT_BATTERY_PARAMS = BatteryParameters(
    nominal_capacity=280.0,    # 标称容量(Ah)
    nominal_voltage=3.2,       # 标称电压(V)
    r0_initial=0.050,         # 初始欧姆内阻(Ω) - 匹配PyBaMM实际输出
    r1_initial=0.025,         # 初始极化内阻(Ω) - 匹配PyBaMM实际输出
    c1_initial=3000.0,        # 初始极化电容(F)
    soc_ocv_table=None,       # SOC-OCV查找表(将从虚拟电池系统获取)
)

def create_default_estimator(cell_simulator=None):
    """
    创建默认配置的电池状态估算器
    
    Args:
        cell_simulator: 可选的CellSimulator实例，用于数据接口
        
    Returns:
        BatteryStateEstimator: 配置好的状态估算器
    """
    # 创建电池模型
    battery_model = FirstOrderRCModel(DEFAULT_BATTERY_PARAMS)
    
    # 创建UKF核心
    ukf_core = UKFCore(DEFAULT_UKF_CONFIG)
    
    # 创建数据接口
    data_interface = None
    if cell_simulator is not None:
        data_interface = VirtualBatteryInterface(cell_simulator)
    
    # 创建状态估算器
    estimator = BatteryStateEstimator(
        battery_model=battery_model,
        ukf_core=ukf_core,
        data_interface=data_interface
    )
    
    return estimator

def quick_start_example():
    """
    快速开始示例 - 展示如何使用UKF估算器
    
    Returns:
        str: 示例代码字符串
    """
    example_code = '''
# 快速开始示例
import sys
sys.path.append('battery_simulator')
sys.path.append('ukf_estimator')

from battery_simulator.cell_simulator import CellSimulator
from ukf_estimator import create_default_estimator

# 1. 创建虚拟电池
cell = CellSimulator(cell_id=1, chemistry="LiFePO4")
cell.set_initial_conditions(initial_soc=0.5, initial_temperature=25.0)

# 2. 创建UKF估算器
estimator = create_default_estimator(cell)

# 3. 初始化估算器
estimator.initialize(initial_soc=0.5, initial_temperature=25.0)

# 4. 运行估算循环
for step in range(100):
    # 虚拟电池仿真一步
    current = 2.0  # 2A充电
    cell_state = cell.simulate_step(current, 25.0, 10.0)
    
    # UKF估算
    estimation = estimator.estimate_step(
        voltage=cell_state.voltage,
        current=cell_state.current,
        temperature=cell_state.temperature,
        dt=10.0
    )
    
    print(f"Step {step}: SOC={estimation.soc:.3f}, "
          f"R0={estimation.r0:.4f}Ω, R1={estimation.r1:.4f}Ω")

# 5. 获取估算结果
results = estimator.get_estimation_history()
print(f"估算完成，共{len(results)}个数据点")
'''
    return example_code

# 模块信息
def get_module_info():
    """获取模块信息"""
    return {
        'name': 'UKF Battery State Estimator',
        'version': __version__,
        'description': '基于无迹卡尔曼滤波的电池状态估算系统',
        'features': [
            'SOC实时估算',
            '欧姆内阻估算',
            '极化内阻估算', 
            '参数在线辨识',
            '与虚拟电池系统集成',
            '估算精度验证',
            '可视化分析'
        ],
        'dependencies': [
            'numpy',
            'scipy', 
            'matplotlib',
            'battery_simulator (项目内部模块)'
        ]
    }

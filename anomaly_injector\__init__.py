"""
异常注入系统 - 基于PyBaMM的物理异常注入仿真框架

本模块提供完整的异常注入功能，用于测试和验证电池异常诊断算法。
通过在PyBaMM仿真过程中修改物理参数来实现真实的异常注入。

主要组件:
- 异常类型定义: ResistanceAnomaly, CapacityAnomaly, ThermalAnomaly
- 增强仿真器: EnhancedCellSimulator, EnhancedPackSimulator
- 异常注入器: AnomalyInjector

支持的异常类型:
1. 内阻异常 (ResistanceAnomaly) - 支持MILD/MODERATE/SEVERE三个级别
2. 容量异常 (CapacityAnomaly) - 支持容量衰减模拟
3. 热异常 (ThermalAnomaly) - 支持热管理异常模拟

作者: Augment Agent
版本: 2.0.0
日期: 2025-01-24
"""

from .anomaly_types import (
    AnomalyType,
    AnomalySeverity,
    ResistanceAnomaly,
    CapacityAnomaly,
    ShortCircuitAnomaly,
    ThermalAnomaly
)

from .anomaly_injector import (
    AnomalyInjector,
    InjectionResult
)

from .enhanced_simulators import (
    EnhancedCellSimulator,
    EnhancedPackSimulator
)

__version__ = "2.0.0"
__author__ = "Augment Agent"
__date__ = "2025-01-24"

__all__ = [
    # 异常类型
    'AnomalyType',
    'AnomalySeverity',
    'ResistanceAnomaly',
    'CapacityAnomaly',
    'ShortCircuitAnomaly',
    'ThermalAnomaly',

    # 异常注入器
    'AnomalyInjector',
    'InjectionResult',

    # 增强仿真器
    'EnhancedCellSimulator',
    'EnhancedPackSimulator',
]

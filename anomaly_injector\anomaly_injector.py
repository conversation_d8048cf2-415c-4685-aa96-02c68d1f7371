"""
异常注入器核心

提供异常注入的基础数据结构和状态管理。
主要的异常注入功能由EnhancedCellSimulator实现。
"""

import logging
from dataclasses import dataclass, field
from typing import Dict, Optional, Any, List
from enum import Enum


class InjectionStatus(Enum):
    """注入状态"""
    PENDING = "pending"         # 等待中
    ACTIVE = "active"          # 激活中
    COMPLETED = "completed"    # 已完成
    FAILED = "failed"          # 失败


@dataclass
class InjectionResult:
    """注入结果"""
    event_id: str
    cell_id: int
    status: InjectionStatus
    injection_time: float
    success: bool
    error_message: Optional[str] = None

    # 注入前后的参数对比
    parameters_before: Dict[str, Any] = field(default_factory=dict)
    parameters_after: Dict[str, Any] = field(default_factory=dict)


class AnomalyInjector:
    """
    异常注入器

    提供异常注入的状态管理和结果跟踪功能。
    实际的异常注入由EnhancedCellSimulator执行。

    注意：这是一个简化的实现，主要用于兼容性。
    推荐直接使用EnhancedCellSimulator进行异常注入。
    """

    def __init__(self):
        """初始化异常注入器"""
        self.logger = logging.getLogger(__name__)
        self.injection_results: List[InjectionResult] = []

    def add_result(self, result: InjectionResult):
        """添加注入结果"""
        self.injection_results.append(result)

    def get_statistics(self) -> Dict[str, int]:
        """获取注入统计信息"""
        successful = sum(1 for r in self.injection_results if r.success)
        failed = len(self.injection_results) - successful

        return {
            'total': len(self.injection_results),
            'successful': successful,
            'failed': failed
        }

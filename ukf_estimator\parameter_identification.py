"""
参数辨识模块

提供电池模型参数的离线辨识和在线自适应功能。
支持基于历史数据的参数优化和实时参数调整。
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
from scipy.optimize import minimize, differential_evolution
import logging

from .battery_models import FirstOrderRCModel, BatteryParameters
from .data_interface import MeasurementData


class ParameterIdentifier:
    """
    电池参数辨识器
    
    提供多种参数辨识方法：
    1. 离线批量辨识 - 基于历史数据的全局优化
    2. 在线递推辨识 - 基于滑动窗口的实时参数更新
    3. 混合辨识 - 结合离线和在线方法
    """
    
    def __init__(self, battery_model: FirstOrderRCModel):
        """
        初始化参数辨识器
        
        Args:
            battery_model: 电池模型
        """
        self.battery_model = battery_model
        self.identification_history = []
        
        # 辨识配置
        self.window_size = 100          # 滑动窗口大小
        self.min_data_points = 50       # 最少数据点数
        self.convergence_threshold = 1e-6  # 收敛阈值
        
        logging.info("参数辨识器初始化完成")
    
    def identify_offline(self, measurement_data: List[MeasurementData], 
                        method: str = "differential_evolution") -> Dict[str, float]:
        """
        离线参数辨识
        
        Args:
            measurement_data: 测量数据列表
            method: 优化方法 ("differential_evolution", "minimize")
            
        Returns:
            辨识的参数字典
        """
        if len(measurement_data) < self.min_data_points:
            raise ValueError(f"数据点不足，需要至少{self.min_data_points}个点")
        
        logging.info(f"开始离线参数辨识，数据点数: {len(measurement_data)}")
        
        # 提取数据
        voltages = np.array([d.voltage for d in measurement_data])
        currents = np.array([d.current for d in measurement_data])
        temperatures = np.array([d.temperature for d in measurement_data])
        timestamps = np.array([d.timestamp for d in measurement_data])
        
        # 计算时间步长
        dt_array = np.diff(timestamps)
        dt_mean = np.mean(dt_array)
        
        # 定义目标函数
        def objective_function(params):
            return self._calculate_model_error(params, voltages, currents, temperatures, dt_mean)
        
        # 参数边界
        bounds = [
            (self.battery_model.params.r0_min, self.battery_model.params.r0_max),  # R0
            (self.battery_model.params.r1_min, self.battery_model.params.r1_max),  # R1
            (self.battery_model.params.c1_min, self.battery_model.params.c1_max),  # C1
        ]
        
        # 执行优化
        if method == "differential_evolution":
            result = differential_evolution(
                objective_function, 
                bounds, 
                seed=42,
                maxiter=100,
                popsize=15
            )
        else:
            # 初始猜测
            x0 = [
                self.battery_model.params.r0_initial,
                self.battery_model.params.r1_initial,
                self.battery_model.params.c1_initial
            ]
            
            result = minimize(
                objective_function,
                x0,
                bounds=bounds,
                method='L-BFGS-B'
            )
        
        if result.success:
            identified_params = {
                'r0': result.x[0],
                'r1': result.x[1],
                'c1': result.x[2],
                'cost': result.fun,
                'method': method,
                'data_points': len(measurement_data)
            }
            
            logging.info(f"参数辨识成功: R0={identified_params['r0']:.4f}Ω, "
                        f"R1={identified_params['r1']:.4f}Ω, C1={identified_params['c1']:.1f}F")
            
            return identified_params
        else:
            logging.error(f"参数辨识失败: {result.message}")
            raise RuntimeError(f"参数辨识失败: {result.message}")
    
    def _calculate_model_error(self, params: np.ndarray, voltages: np.ndarray, 
                              currents: np.ndarray, temperatures: np.ndarray, dt: float) -> float:
        """
        计算模型误差
        
        Args:
            params: 参数向量 [R0, R1, C1]
            voltages: 电压数组
            currents: 电流数组
            temperatures: 温度数组
            dt: 时间步长
            
        Returns:
            均方根误差
        """
        try:
            r0, r1, c1 = params
            
            # 约束参数
            r0 = np.clip(r0, self.battery_model.params.r0_min, self.battery_model.params.r0_max)
            r1 = np.clip(r1, self.battery_model.params.r1_min, self.battery_model.params.r1_max)
            c1 = np.clip(c1, self.battery_model.params.c1_min, self.battery_model.params.c1_max)
            
            # 初始化状态
            soc = 0.5  # 假设初始SOC为50%
            v1 = 0.0   # 初始极化电压为0
            
            predicted_voltages = []
            
            for i in range(len(voltages)):
                current = currents[i]
                temperature = temperatures[i]
                
                # 更新SOC (简化的库仑计法)
                if i > 0:
                    dsoc = current * dt / (3600.0 * self.battery_model.params.nominal_capacity)
                    soc = np.clip(soc + dsoc, 0.0, 1.0)
                
                # 更新极化电压
                tau = r1 * c1
                if tau > 1e-6:
                    exp_factor = np.exp(-dt / tau)
                    v1 = v1 * exp_factor + r1 * current * (1 - exp_factor)
                else:
                    v1 = r1 * current
                
                # 计算预测电压
                ocv = self.battery_model.get_ocv(soc)
                temp_factor = np.exp(self.battery_model.params.temp_coeff_r0 * (25.0 - temperature))
                r0_corrected = r0 * temp_factor
                
                v_pred = ocv - current * r0_corrected - v1
                predicted_voltages.append(v_pred)
            
            # 计算误差
            predicted_voltages = np.array(predicted_voltages)
            errors = voltages - predicted_voltages
            rmse = np.sqrt(np.mean(errors**2))
            
            return rmse
            
        except Exception as e:
            logging.error(f"模型误差计算失败: {e}")
            return 1e6  # 返回大误差值
    
    def identify_online(self, recent_data: List[MeasurementData], 
                       current_params: Dict[str, float]) -> Dict[str, float]:
        """
        在线参数辨识 (递推最小二乘法)
        
        Args:
            recent_data: 最近的测量数据
            current_params: 当前参数估计
            
        Returns:
            更新后的参数
        """
        if len(recent_data) < 10:  # 需要足够的数据点
            return current_params
        
        try:
            # 使用简化的递推最小二乘法
            # 这里实现一个基础版本，可以根据需要扩展
            
            # 提取最近的数据
            voltages = np.array([d.voltage for d in recent_data[-self.window_size:]])
            currents = np.array([d.current for d in recent_data[-self.window_size:]])
            
            # 计算电压变化率作为特征
            if len(voltages) > 1:
                dv_dt = np.diff(voltages)
                di_dt = np.diff(currents)
                
                # 简单的线性回归估计内阻
                if np.std(di_dt) > 1e-3:  # 电流变化足够大
                    # R0 ≈ -dV/dI (简化估计)
                    r0_est = -np.mean(dv_dt / (di_dt + 1e-6))
                    r0_est = np.clip(r0_est, self.battery_model.params.r0_min, 
                                   self.battery_model.params.r0_max)
                    
                    # 使用指数移动平均更新参数
                    alpha = 0.1  # 学习率
                    updated_params = current_params.copy()
                    updated_params['r0'] = (1 - alpha) * current_params['r0'] + alpha * r0_est
                    
                    logging.debug(f"在线参数更新: R0 {current_params['r0']:.4f} -> {updated_params['r0']:.4f}")
                    return updated_params
            
            return current_params
            
        except Exception as e:
            logging.error(f"在线参数辨识失败: {e}")
            return current_params
    
    def validate_parameters(self, params: Dict[str, float], 
                           validation_data: List[MeasurementData]) -> Dict[str, float]:
        """
        验证参数质量
        
        Args:
            params: 待验证的参数
            validation_data: 验证数据
            
        Returns:
            验证指标
        """
        if not validation_data:
            return {}
        
        try:
            # 使用参数计算预测误差
            voltages = np.array([d.voltage for d in validation_data])
            currents = np.array([d.current for d in validation_data])
            temperatures = np.array([d.temperature for d in validation_data])
            timestamps = np.array([d.timestamp for d in validation_data])
            
            dt_mean = np.mean(np.diff(timestamps))
            
            # 计算模型误差
            param_array = [params.get('r0', 0.003), params.get('r1', 0.002), params.get('c1', 3000.0)]
            rmse = self._calculate_model_error(param_array, voltages, currents, temperatures, dt_mean)
            
            # 计算其他指标
            max_error = np.max(np.abs(voltages - np.mean(voltages)))  # 简化计算
            
            return {
                'rmse': rmse,
                'max_error': max_error,
                'data_points': len(validation_data),
                'r0_value': params.get('r0', 0.0),
                'r1_value': params.get('r1', 0.0),
                'c1_value': params.get('c1', 0.0)
            }
            
        except Exception as e:
            logging.error(f"参数验证失败: {e}")
            return {}
    
    def get_parameter_bounds(self) -> Dict[str, Tuple[float, float]]:
        """获取参数边界"""
        return {
            'r0': (self.battery_model.params.r0_min, self.battery_model.params.r0_max),
            'r1': (self.battery_model.params.r1_min, self.battery_model.params.r1_max),
            'c1': (self.battery_model.params.c1_min, self.battery_model.params.c1_max)
        }
    
    def update_model_parameters(self, new_params: Dict[str, float]):
        """更新模型参数"""
        if 'r0' in new_params:
            self.battery_model.params.r0_initial = new_params['r0']
        if 'r1' in new_params:
            self.battery_model.params.r1_initial = new_params['r1']
        if 'c1' in new_params:
            self.battery_model.params.c1_initial = new_params['c1']
        
        logging.info(f"模型参数已更新: {new_params}")
    
    def get_identification_history(self) -> List[Dict[str, float]]:
        """获取辨识历史"""
        return self.identification_history.copy()

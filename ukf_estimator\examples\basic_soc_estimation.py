#!/usr/bin/env python3
"""
基础SOC估算示例

演示如何使用UKF估算器进行基本的SOC估算。
与虚拟电池系统集成，展示完整的估算流程。
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt
import logging

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'battery_simulator'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def main():
    """主函数"""
    print("🔋 UKF基础SOC估算示例")
    print("=" * 60)
    
    try:
        # 1. 导入必要模块
        from battery_simulator.cell_simulator import CellSimulator
        from ukf_estimator import (
            create_default_estimator, 
            BatteryStateEstimator,
            EstimationVisualizer,
            EstimationValidator
        )
        
        print("✅ 模块导入成功")
        
        # 2. 创建虚拟电池
        print("\n📦 创建虚拟电池系统...")
        cell = CellSimulator(cell_id=1, chemistry="LiFePO4", include_thermal=True)
        cell.set_initial_conditions(initial_soc=0.6, initial_temperature=25.0)
        print(f"✅ 虚拟电池创建成功 (初始SOC: 60%)")
        
        # 3. 创建UKF估算器
        print("\n🧮 创建UKF估算器...")
        estimator = create_default_estimator(cell)
        estimator.initialize(initial_soc=0.5, initial_temperature=25.0)  # 故意设置不同的初始值
        print(f"✅ UKF估算器初始化完成 (初始估算SOC: 50%)")
        
        # 4. 运行估算仿真
        print("\n🔄 开始估算仿真...")
        
        # 仿真参数
        total_steps = 200
        dt = 10.0  # 10秒步长
        
        # 电流配置文件 (模拟实际使用场景)
        current_profile = []
        for step in range(total_steps):
            if step < 50:
                current = 2.0  # 2A充电
            elif step < 100:
                current = 0.0  # 静置
            elif step < 150:
                current = -3.0  # 3A放电
            else:
                current = 1.0  # 1A充电
            current_profile.append(current)
        
        # 存储结果
        estimation_results = []
        ground_truth_data = []
        
        print(f"运行{total_steps}步仿真，每步{dt}秒...")
        
        for step in range(total_steps):
            current = current_profile[step]
            
            # 虚拟电池仿真一步
            cell_state = cell.simulate_step(current, 25.0, dt)
            
            # UKF估算一步
            estimation = estimator.estimate_step(
                voltage=cell_state.voltage,
                current=cell_state.current,
                temperature=cell_state.temperature,
                dt=dt
            )
            
            # 保存结果
            estimation_results.append(estimation)
            
            # 保存真实值用于验证
            from ukf_estimator.data_interface import MeasurementData
            ground_truth = MeasurementData(
                timestamp=cell_state.timestamp,
                voltage=cell_state.voltage,
                current=cell_state.current,
                temperature=cell_state.temperature,
                soc_true=cell_state.soc,
                r0_true=cell_state.internal_resistance,
                is_valid=cell_state.is_valid
            )
            ground_truth_data.append(ground_truth)
            
            # 每50步显示进度
            if (step + 1) % 50 == 0:
                print(f"  步骤 {step+1}/{total_steps}: "
                      f"真实SOC={cell_state.soc:.3f}, "
                      f"估算SOC={estimation.soc:.3f}, "
                      f"误差={abs(cell_state.soc - estimation.soc):.3f}")
        
        print("✅ 仿真完成")
        
        # 5. 性能评估
        print("\n📊 性能评估...")
        validator = EstimationValidator()
        metrics = validator.validate_estimation_results(estimation_results, ground_truth_data)
        
        print(f"SOC估算性能:")
        print(f"  - RMSE: {metrics.soc_rmse:.4f}")
        print(f"  - MAE: {metrics.soc_mae:.4f}")
        print(f"  - 最大误差: {metrics.soc_max_error:.4f}")
        print(f"  - 相关系数: {metrics.soc_correlation:.4f}")
        
        print(f"内阻估算性能:")
        print(f"  - RMSE: {metrics.r0_rmse*1000:.2f} mΩ")
        print(f"  - MAE: {metrics.r0_mae*1000:.2f} mΩ")
        
        print(f"系统性能:")
        print(f"  - 成功率: {metrics.estimation_success_rate:.1%}")
        print(f"  - 收敛时间: {metrics.convergence_time:.1f} s")
        
        # 6. 可视化结果
        print("\n📈 生成可视化图表...")
        visualizer = EstimationVisualizer()
        
        # 估算结果对比图
        fig1 = visualizer.plot_estimation_results(
            estimation_results, 
            ground_truth_data,
            save_path="basic_soc_estimation_results.png"
        )
        
        # 参数收敛图
        fig2 = visualizer.plot_parameter_convergence(
            estimation_results,
            save_path="basic_soc_parameter_convergence.png"
        )
        
        # 验证指标图
        fig3 = visualizer.plot_validation_metrics(
            metrics,
            save_path="basic_soc_validation_metrics.png"
        )
        
        print("✅ 图表已保存:")
        print("  - basic_soc_estimation_results.png")
        print("  - basic_soc_parameter_convergence.png") 
        print("  - basic_soc_validation_metrics.png")
        
        # 7. 生成详细报告
        print("\n📋 生成验证报告...")
        report = validator.generate_validation_report(metrics)
        
        with open("basic_soc_estimation_report.txt", "w", encoding="utf-8") as f:
            f.write(report)
        
        print("✅ 验证报告已保存: basic_soc_estimation_report.txt")
        
        # 8. 显示最终统计
        print("\n" + "=" * 60)
        print("🎯 基础SOC估算示例完成!")
        print(f"📊 处理了 {len(estimation_results)} 个数据点")
        print(f"🎯 SOC估算精度: RMSE = {metrics.soc_rmse:.1%}")
        print(f"⚡ 内阻估算精度: RMSE = {metrics.r0_rmse*1000:.1f} mΩ")
        
        # 性能评级
        if metrics.soc_rmse < 0.02:
            print("🏆 SOC估算性能: 优秀")
        elif metrics.soc_rmse < 0.05:
            print("👍 SOC估算性能: 良好")
        else:
            print("⚠️  SOC估算性能: 需要改进")
        
        # 显示图表
        plt.show()
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("请确保已正确安装所有依赖模块")
        return False
        
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        logging.error(f"示例运行失败: {e}", exc_info=True)
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 示例运行成功!")
    else:
        print("\n❌ 示例运行失败!")
        sys.exit(1)

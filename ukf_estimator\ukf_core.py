"""
无迹卡尔曼滤波(UKF)核心算法实现

实现标准的UKF算法，包括sigma点生成、预测步骤、更新步骤等。
专门针对电池状态估算进行了优化。
"""

import numpy as np
import scipy.linalg
from typing import Dict, Tuple, Optional, Callable
from dataclasses import dataclass
from threading import Lock
from copy import deepcopy
import logging


class UKFException(Exception):
    """UKF异常类，用于处理UKF相关的错误"""
    pass


@dataclass
class UKFConfig:
    """UKF配置参数"""
    alpha: float = 1e-3          # 控制sigma点分布的参数 (通常1e-4到1e-1)
    beta: float = 2.0            # 用于高阶矩匹配 (对于高斯分布，最优值为2)
    kappa: float = 0.0           # 次要缩放参数 (通常设为0或3-n)
    process_noise_std: Dict[str, float] = None  # 过程噪声标准差
    measurement_noise_std: float = 5e-3         # 测量噪声标准差
    thread_safe: bool = True     # 是否启用线程安全

    def __post_init__(self):
        if self.process_noise_std is None:
            self.process_noise_std = {
                'soc': 1e-4,     # SOC过程噪声
                'v1': 1e-3,      # 极化电压过程噪声
                'r0': 1e-6,      # 欧姆内阻过程噪声
                'r1': 1e-6,      # 极化内阻过程噪声
                'c1': 1e-4,      # 极化电容过程噪声
            }


class UKFCore:
    """
    无迹卡尔曼滤波核心算法
    
    实现标准UKF算法，支持非线性状态方程和观测方程。
    针对电池状态估算进行了优化，包括数值稳定性改进。
    """
    
    def __init__(self, config: UKFConfig):
        """
        初始化UKF核心

        Args:
            config: UKF配置参数
        """
        self.config = config
        self.n_states = 0  # 状态维度，将在初始化时设置
        self.n_sigma = 0   # sigma点数量

        # UKF权重
        self.weights_mean = None    # 均值权重
        self.weights_cov = None     # 协方差权重
        self.lambda_param = None    # UKF缩放参数

        # 噪声协方差矩阵
        self.Q = None  # 过程噪声协方差
        self.R = None  # 测量噪声协方差

        # 当前状态
        self.x = None  # 状态向量
        self.P = None  # 状态协方差矩阵
        self.sigma_points = None  # 当前sigma点

        # 历史记录
        self.estimation_history = []

        # 线程安全
        self.lock = Lock() if config.thread_safe else None

        logging.info("UKF核心算法初始化完成")
    
    def initialize(self, initial_state: np.ndarray, initial_covariance: np.ndarray,
                   process_noise_cov: np.ndarray, measurement_noise_var: float):
        """
        初始化UKF滤波器
        
        Args:
            initial_state: 初始状态向量
            initial_covariance: 初始状态协方差矩阵
            process_noise_cov: 过程噪声协方差矩阵
            measurement_noise_var: 测量噪声方差
        """
        self.n_states = len(initial_state)
        self.n_sigma = 2 * self.n_states + 1
        
        # 设置状态和协方差
        self.x = initial_state.copy()
        self.P = initial_covariance.copy()
        
        # 设置噪声协方差
        self.Q = process_noise_cov.copy()
        self.R = measurement_noise_var
        
        # 计算UKF权重
        self._calculate_weights()
        
        logging.info(f"UKF初始化: 状态维度={self.n_states}, sigma点数={self.n_sigma}")
    
    def _calculate_weights(self):
        """计算UKF权重 - 借鉴优化的权重计算方法"""
        self.lambda_param = self.config.alpha**2 * (self.n_states + self.config.kappa) - self.n_states

        # 均值权重
        self.weights_mean = np.zeros(self.n_sigma)
        self.weights_mean[0] = self.lambda_param / (self.n_states + self.lambda_param)

        # 协方差权重
        self.weights_cov = np.zeros(self.n_sigma)
        self.weights_cov[0] = (self.lambda_param / (self.n_states + self.lambda_param)) + \
                             (1 - self.config.alpha**2 + self.config.beta)

        # 其余权重
        for i in range(1, self.n_sigma):
            self.weights_mean[i] = 1.0 / (2 * (self.n_states + self.lambda_param))
            self.weights_cov[i] = 1.0 / (2 * (self.n_states + self.lambda_param))

        logging.debug(f"UKF权重计算完成: λ={self.lambda_param:.6f}")
    
    def _generate_sigma_points(self, x: np.ndarray, P: np.ndarray) -> np.ndarray:
        """
        生成sigma点 - 借鉴更高效的实现方法

        Args:
            x: 状态向量
            P: 协方差矩阵

        Returns:
            sigma点矩阵 (n_states × n_sigma) - 注意维度调整
        """
        n = len(x)

        # 计算缩放后的协方差矩阵
        scaled_cov = (n + self.lambda_param) * P

        # 优先使用scipy的矩阵平方根计算 (更稳定)
        try:
            sqrt_matrix = scipy.linalg.sqrtm(scaled_cov)
            # 确保结果是实数
            if np.iscomplexobj(sqrt_matrix):
                sqrt_matrix = sqrt_matrix.real
        except (np.linalg.LinAlgError, ValueError):
            # 备用方案：Cholesky分解
            try:
                sqrt_matrix = np.linalg.cholesky(scaled_cov)
            except np.linalg.LinAlgError:
                # 最后备用方案：特征值分解
                eigenvals, eigenvecs = np.linalg.eigh(scaled_cov)
                eigenvals = np.maximum(eigenvals, 1e-12)  # 确保正定性
                sqrt_matrix = eigenvecs @ np.diag(np.sqrt(eigenvals))

        # 生成sigma点 (返回 n_states × n_sigma 格式)
        sigma_points = np.zeros((n, self.n_sigma))
        sigma_points[:, 0] = x

        for i in range(n):
            sigma_points[:, i + 1] = x + sqrt_matrix[i]
            sigma_points[:, i + 1 + n] = x - sqrt_matrix[i]

        return sigma_points
    
    def predict(self, state_transition_func: Callable, dt: float,
                control_input: Optional[np.ndarray] = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        UKF预测步骤 - 借鉴更高效的向量化实现

        Args:
            state_transition_func: 状态转移函数 f(x, dt, u)
            dt: 时间步长
            control_input: 控制输入 (可选)

        Returns:
            (预测状态, 预测协方差)
        """
        # 线程安全保护
        if self.lock:
            self.lock.acquire()

        try:
            # 生成sigma点
            self.sigma_points = self._generate_sigma_points(self.x, self.P)

            # 向量化传播sigma点 - 借鉴更高效的实现
            sigma_points_pred = np.array([
                state_transition_func(self.sigma_points[:, i], dt, control_input)
                for i in range(self.n_sigma)
            ]).T  # 转置为 n_states × n_sigma

            # 计算预测均值 - 使用向量化操作
            x_pred = np.sum(sigma_points_pred * self.weights_mean, axis=1)

            # 计算预测协方差 - 优化的实现
            P_pred = np.zeros((self.n_states, self.n_states))
            for i in range(self.n_sigma):
                diff = sigma_points_pred[:, i] - x_pred
                diff = np.atleast_2d(diff).T  # 确保列向量
                P_pred += self.weights_cov[i] * np.dot(diff, diff.T)

            # 添加过程噪声 (考虑时间步长，但限制最大值)
            noise_scaling = min(dt, 1.0)  # 限制噪声缩放
            P_pred += noise_scaling * self.Q

            # 更新状态
            self.sigma_points = sigma_points_pred
            self.x = x_pred
            self.P = P_pred

            return x_pred, P_pred

        finally:
            if self.lock:
                self.lock.release()
    
    def update(self, measurement: float, observation_func: Callable) -> Tuple[np.ndarray, np.ndarray]:
        """
        UKF更新步骤 - 借鉴更稳定的实现

        Args:
            measurement: 测量值
            observation_func: 观测函数 h(x)

        Returns:
            (更新后状态, 更新后协方差)
        """
        # 线程安全保护
        if self.lock:
            self.lock.acquire()

        try:
            # 使用当前sigma点或重新生成
            if self.sigma_points is None:
                sigma_points = self._generate_sigma_points(self.x, self.P)
            else:
                sigma_points = self.sigma_points

            # 向量化传播sigma点通过观测函数
            sigma_measurements = np.array([
                observation_func(sigma_points[:, i]) for i in range(self.n_sigma)
            ])

            # 计算预测测量均值
            z_pred = np.sum(self.weights_mean * sigma_measurements)

            # 计算测量协方差和交叉协方差 - 优化实现
            Pzz = self.R  # 测量噪声
            Pxz = np.zeros(self.n_states)

            for i in range(self.n_sigma):
                diff_z = sigma_measurements[i] - z_pred
                diff_x = sigma_points[:, i] - self.x

                Pzz += self.weights_cov[i] * diff_z * diff_z
                Pxz += self.weights_cov[i] * diff_x * diff_z

            # 计算卡尔曼增益 - 添加数值稳定性检查
            if abs(Pzz) < 1e-12:
                raise UKFException("Measurement covariance matrix is near singular")

            K = Pxz / Pzz

            # 限制卡尔曼增益的大小，避免过度修正
            K = np.clip(K, -10.0, 10.0)

            # 更新状态和协方差
            innovation = measurement - z_pred
            x_new = self.x + K * innovation

            # 检查状态向量的有效性
            if not np.all(np.isfinite(x_new)):
                raise UKFException("State vector contains invalid values")

            # 调试信息已移除

            self.x = x_new

            # 使用标准的协方差更新 (避免Joseph形式的数值问题)
            self.P = self.P - np.outer(K, K) * Pzz

            # 确保协方差矩阵的正定性和对称性
            self.P = (self.P + self.P.T) / 2  # 确保对称性

            # 检查数值有效性
            if not np.all(np.isfinite(self.P)):
                # 重置为保守的协方差矩阵
                self.P = np.eye(self.n_states) * 0.01
            else:
                # 确保正定性
                try:
                    eigenvals = np.linalg.eigvals(self.P)
                    if np.any(eigenvals <= 1e-8):
                        self.P += np.eye(self.n_states) * 1e-6
                except:
                    # 如果特征值计算失败，重置协方差矩阵
                    self.P = np.eye(self.n_states) * 0.01

            return self.x, self.P

        finally:
            if self.lock:
                self.lock.release()
    
    def get_state(self, index: Optional[int] = None) -> np.ndarray:
        """
        获取状态向量或特定状态分量 - 借鉴灵活的状态访问接口

        Args:
            index: 状态分量索引，None表示返回完整状态向量

        Returns:
            状态向量或状态分量
        """
        if self.x is None:
            raise UKFException("UKF未初始化")

        if index is None:
            return self.x.copy()
        else:
            if 0 <= index < len(self.x):
                return self.x[index]
            else:
                raise IndexError(f"状态索引{index}超出范围[0, {len(self.x)-1}]")

    def set_state(self, state: np.ndarray, covariance: Optional[np.ndarray] = None):
        """
        设置状态向量 - 借鉴灵活的状态设置接口

        Args:
            state: 新的状态向量
            covariance: 新的协方差矩阵 (可选)
        """
        if self.lock:
            self.lock.acquire()

        try:
            self.x = np.array(state, dtype=float)

            if covariance is not None:
                self.P = np.array(covariance, dtype=float)
                # 验证协方差矩阵
                if self.P.shape != (len(self.x), len(self.x)):
                    raise ValueError("协方差矩阵维度不匹配")

            logging.debug("状态向量已更新")

        finally:
            if self.lock:
                self.lock.release()

    def get_covariance(self) -> np.ndarray:
        """获取协方差矩阵"""
        if self.P is None:
            raise UKFException("UKF未初始化")
        return self.P.copy()

    def get_current_state(self) -> Dict[str, float]:
        """
        获取当前状态 - 增强版本

        Returns:
            当前状态字典
        """
        if self.x is None:
            return {}

        return {
            'state_vector': self.x.copy(),
            'covariance_matrix': self.P.copy(),
            'state_uncertainty': np.sqrt(np.diag(self.P)),
            'condition_number': np.linalg.cond(self.P),
            'determinant': np.linalg.det(self.P),
            'trace': np.trace(self.P)
        }

    def reset(self, state: Optional[np.ndarray] = None, covariance: Optional[np.ndarray] = None):
        """
        重置滤波器状态 - 借鉴更灵活的重置接口

        Args:
            state: 可选的初始状态
            covariance: 可选的初始协方差
        """
        if self.lock:
            self.lock.acquire()

        try:
            if state is not None:
                self.x = np.array(state, dtype=float)
            else:
                self.x = None

            if covariance is not None:
                self.P = np.array(covariance, dtype=float)
            else:
                self.P = None

            self.sigma_points = None
            self.estimation_history.clear()

            logging.info("UKF状态已重置")

        finally:
            if self.lock:
                self.lock.release()

    def is_initialized(self) -> bool:
        """检查UKF是否已初始化"""
        return self.x is not None and self.P is not None

    def get_sigma_points(self) -> Optional[np.ndarray]:
        """获取当前sigma点 - 用于调试和分析"""
        return self.sigma_points.copy() if self.sigma_points is not None else None

    def validate_state(self) -> bool:
        """
        验证当前状态的有效性

        Returns:
            状态是否有效
        """
        if not self.is_initialized():
            return False

        # 检查状态向量
        if not np.all(np.isfinite(self.x)):
            logging.warning("状态向量包含无效值")
            return False

        # 检查协方差矩阵
        if not np.all(np.isfinite(self.P)):
            logging.warning("协方差矩阵包含无效值")
            return False

        # 检查协方差矩阵正定性
        eigenvals = np.linalg.eigvals(self.P)
        if np.any(eigenvals <= 0):
            logging.warning("协方差矩阵不是正定的")
            return False

        return True

    def get_innovation_statistics(self, measurements: list, observation_func: Callable) -> Dict[str, float]:
        """
        计算新息统计 - 用于滤波器性能评估

        Args:
            measurements: 测量值列表
            observation_func: 观测函数

        Returns:
            新息统计字典
        """
        if not self.is_initialized():
            raise UKFException("UKF未初始化")

        innovations = []
        for measurement in measurements:
            # 预测测量值
            if self.sigma_points is None:
                sigma_points = self._generate_sigma_points(self.x, self.P)
            else:
                sigma_points = self.sigma_points

            sigma_measurements = np.array([
                observation_func(sigma_points[:, i]) for i in range(self.n_sigma)
            ])

            z_pred = np.sum(self.weights_mean * sigma_measurements)
            innovation = measurement - z_pred
            innovations.append(innovation)

        innovations = np.array(innovations)

        return {
            'mean_innovation': np.mean(innovations),
            'std_innovation': np.std(innovations),
            'max_innovation': np.max(np.abs(innovations)),
            'innovation_sequence': innovations.tolist()
        }

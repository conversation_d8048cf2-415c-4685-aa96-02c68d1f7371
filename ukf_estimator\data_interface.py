"""
数据接口模块

提供与虚拟电池系统的数据接口，支持实时数据获取和历史数据管理。
确保UKF估算器能够无缝集成到现有的电池仿真系统中。
"""

import sys
import os
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from collections import deque
import logging
import time

# 添加battery_simulator路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'battery_simulator'))

try:
    from cell_simulator import CellSimulator, CellState
    from pack_simulator import BatteryPackSimulator, PackState
    BATTERY_SIMULATOR_AVAILABLE = True
except ImportError as e:
    logging.warning(f"无法导入battery_simulator: {e}")
    BATTERY_SIMULATOR_AVAILABLE = False
    # 创建占位符类
    class CellSimulator:
        pass
    class BatteryPackSimulator:
        pass
    class CellState:
        pass
    class PackState:
        pass


@dataclass
class MeasurementData:
    """测量数据结构"""
    timestamp: float
    voltage: float          # 端电压 (V)
    current: float          # 电流 (A, 正为充电)
    temperature: float      # 温度 (°C)
    soc_true: Optional[float] = None      # 真实SOC (仅用于验证)
    r0_true: Optional[float] = None       # 真实欧姆内阻 (仅用于验证)
    is_valid: bool = True


class DataBuffer:
    """数据缓冲区"""
    
    def __init__(self, max_size: int = 1000):
        """
        初始化数据缓冲区
        
        Args:
            max_size: 最大缓冲区大小
        """
        self.max_size = max_size
        self.data = deque(maxlen=max_size)
        self.timestamps = deque(maxlen=max_size)
        
    def add_data(self, data_point: Dict[str, Any]):
        """添加数据点"""
        timestamp = data_point.get('timestamp', time.time())
        self.data.append(data_point)
        self.timestamps.append(timestamp)
    
    def get_recent_data(self, n_points: int) -> List[Dict[str, Any]]:
        """获取最近n个数据点"""
        return list(self.data)[-n_points:]
    
    def get_data_in_window(self, time_window: float) -> List[Dict[str, Any]]:
        """获取时间窗口内的数据"""
        if not self.timestamps:
            return []
        
        current_time = self.timestamps[-1]
        start_time = current_time - time_window
        
        result = []
        for i, timestamp in enumerate(self.timestamps):
            if timestamp >= start_time:
                result.append(self.data[i])
        
        return result
    
    def clear(self):
        """清空缓冲区"""
        self.data.clear()
        self.timestamps.clear()
    
    def size(self) -> int:
        """获取当前大小"""
        return len(self.data)


class VirtualBatteryInterface:
    """
    虚拟电池系统数据接口
    
    提供与battery_simulator模块的集成接口，支持从CellSimulator
    和BatteryPackSimulator获取实时数据用于UKF估算。
    """
    
    def __init__(self, simulator, cell_id: Optional[int] = None):
        """
        初始化数据接口
        
        Args:
            simulator: CellSimulator或BatteryPackSimulator实例
            cell_id: 如果是电池包仿真器，指定要监控的电芯ID
        """
        if not BATTERY_SIMULATOR_AVAILABLE:
            raise RuntimeError("battery_simulator模块不可用")
        
        self.simulator = simulator
        self.cell_id = cell_id
        self.is_pack_simulator = isinstance(simulator, BatteryPackSimulator)
        
        # 数据缓冲
        self.measurement_buffer = DataBuffer(max_size=10000)
        self.last_measurement_time = 0.0
        
        # 数据质量监控
        self.total_measurements = 0
        self.valid_measurements = 0
        self.measurement_errors = []
        
        logging.info(f"虚拟电池接口初始化: {'电池包' if self.is_pack_simulator else '单电芯'}仿真器")
    
    def get_measurement(self) -> Optional[MeasurementData]:
        """
        从虚拟电池系统获取当前测量数据
        
        Returns:
            测量数据，如果获取失败返回None
        """
        try:
            if self.is_pack_simulator:
                return self._get_pack_measurement()
            else:
                return self._get_cell_measurement()
        except Exception as e:
            logging.error(f"获取测量数据失败: {e}")
            return None
    
    def _get_cell_measurement(self) -> Optional[MeasurementData]:
        """从单电芯仿真器获取数据"""
        try:
            cell_state = self.simulator.get_current_state()
            if cell_state is None:
                return None
            
            measurement = MeasurementData(
                timestamp=cell_state.timestamp,
                voltage=cell_state.voltage,
                current=cell_state.current,
                temperature=cell_state.temperature,
                soc_true=cell_state.soc,  # 真实SOC用于验证
                r0_true=cell_state.internal_resistance,  # 真实内阻用于验证
                is_valid=cell_state.is_valid
            )
            
            # 数据质量检查
            if self._validate_measurement(measurement):
                self.measurement_buffer.add_data({
                    'timestamp': measurement.timestamp,
                    'voltage': measurement.voltage,
                    'current': measurement.current,
                    'temperature': measurement.temperature,
                    'soc_true': measurement.soc_true,
                    'r0_true': measurement.r0_true
                })
                
                self.valid_measurements += 1
            
            self.total_measurements += 1
            self.last_measurement_time = measurement.timestamp
            
            return measurement
            
        except Exception as e:
            logging.error(f"单电芯数据获取失败: {e}")
            return None
    
    def _get_pack_measurement(self) -> Optional[MeasurementData]:
        """从电池包仿真器获取数据"""
        try:
            # 这里需要实现从电池包获取特定电芯数据的逻辑
            # 由于当前的pack_simulator可能没有直接的接口，我们先实现基础版本
            
            # 假设电池包仿真器有get_current_pack_state方法
            if hasattr(self.simulator, 'get_current_pack_state'):
                pack_state = self.simulator.get_current_pack_state()
                if pack_state is None or not pack_state.cell_states:
                    return None
                
                # 选择目标电芯
                if self.cell_id is not None and self.cell_id < len(pack_state.cell_states):
                    cell_state = pack_state.cell_states[self.cell_id]
                else:
                    cell_state = pack_state.cell_states[0]  # 默认选择第一个电芯
                
                measurement = MeasurementData(
                    timestamp=pack_state.timestamp,
                    voltage=cell_state.voltage,
                    current=cell_state.current,
                    temperature=cell_state.temperature,
                    soc_true=cell_state.soc,
                    r0_true=cell_state.internal_resistance,
                    is_valid=cell_state.is_valid
                )
                
                if self._validate_measurement(measurement):
                    self.measurement_buffer.add_data({
                        'timestamp': measurement.timestamp,
                        'voltage': measurement.voltage,
                        'current': measurement.current,
                        'temperature': measurement.temperature,
                        'soc_true': measurement.soc_true,
                        'r0_true': measurement.r0_true
                    })
                    self.valid_measurements += 1
                
                self.total_measurements += 1
                return measurement
            else:
                logging.warning("电池包仿真器不支持状态获取")
                return None
                
        except Exception as e:
            logging.error(f"电池包数据获取失败: {e}")
            return None
    
    def _validate_measurement(self, measurement: MeasurementData) -> bool:
        """验证测量数据质量"""
        try:
            # 基本范围检查
            if not (2.0 <= measurement.voltage <= 4.5):
                logging.warning(f"电压超出合理范围: {measurement.voltage}V")
                return False
            
            if not (-200.0 <= measurement.current <= 200.0):
                logging.warning(f"电流超出合理范围: {measurement.current}A")
                return False
            
            if not (-20.0 <= measurement.temperature <= 80.0):
                logging.warning(f"温度超出合理范围: {measurement.temperature}°C")
                return False
            
            if measurement.soc_true is not None and not (0.0 <= measurement.soc_true <= 1.0):
                logging.warning(f"SOC超出合理范围: {measurement.soc_true}")
                return False
            
            # 检查数值有效性
            if not all(np.isfinite([measurement.voltage, measurement.current, measurement.temperature])):
                logging.warning("测量数据包含无效数值")
                return False
            
            return True
            
        except Exception as e:
            logging.error(f"数据验证失败: {e}")
            return False
    
    def simulate_and_measure(self, current: float, temperature: float, dt: float) -> Optional[MeasurementData]:
        """
        执行仿真步骤并获取测量数据
        
        Args:
            current: 输入电流 (A)
            temperature: 环境温度 (°C)
            dt: 时间步长 (s)
            
        Returns:
            测量数据
        """
        try:
            if self.is_pack_simulator:
                # 电池包仿真
                if hasattr(self.simulator, 'simulate_pack_step'):
                    pack_state = self.simulator.simulate_pack_step(current, temperature, dt)
                    if pack_state and pack_state.cell_states:
                        cell_state = pack_state.cell_states[self.cell_id or 0]
                        return MeasurementData(
                            timestamp=pack_state.timestamp,
                            voltage=cell_state.voltage,
                            current=cell_state.current,
                            temperature=cell_state.temperature,
                            soc_true=cell_state.soc,
                            r0_true=cell_state.internal_resistance,
                            is_valid=cell_state.is_valid
                        )
            else:
                # 单电芯仿真
                cell_state = self.simulator.simulate_step(current, temperature, dt)
                return MeasurementData(
                    timestamp=cell_state.timestamp,
                    voltage=cell_state.voltage,
                    current=cell_state.current,
                    temperature=cell_state.temperature,
                    soc_true=cell_state.soc,
                    r0_true=cell_state.internal_resistance,
                    is_valid=cell_state.is_valid
                )
            
            return None
            
        except Exception as e:
            logging.error(f"仿真和测量失败: {e}")
            return None
    
    def get_measurement_history(self, n_points: Optional[int] = None) -> List[Dict[str, Any]]:
        """获取测量历史"""
        if n_points is None:
            return list(self.measurement_buffer.data)
        else:
            return self.measurement_buffer.get_recent_data(n_points)
    
    def get_data_quality_metrics(self) -> Dict[str, float]:
        """获取数据质量指标"""
        if self.total_measurements == 0:
            return {}
        
        return {
            'total_measurements': self.total_measurements,
            'valid_measurements': self.valid_measurements,
            'data_quality_rate': self.valid_measurements / self.total_measurements,
            'buffer_size': self.measurement_buffer.size(),
            'last_measurement_time': self.last_measurement_time
        }
    
    def reset(self):
        """重置接口状态"""
        self.measurement_buffer.clear()
        self.total_measurements = 0
        self.valid_measurements = 0
        self.measurement_errors.clear()
        self.last_measurement_time = 0.0
        
        logging.info("虚拟电池接口已重置")

"""
单电芯PyBaMM仿真器

基于PyBaMM的单电芯高精度电化学仿真，提供纯净的物理数据输出。
专注于单电芯的电化学行为，不包含异常注入。
"""

import numpy as np
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

try:
    import pybamm
    from pybamm_adapter import PyBaMMVersionAdapter
    PYBAMM_AVAILABLE = True
    logging.info(f"PyBaMM可用: 版本 {pybamm.__version__}")
except ImportError:
    PYBAMM_AVAILABLE = False
    PyBaMMVersionAdapter = None
    logging.error("PyBaMM不可用！这是系统的核心依赖，请安装PyBaMM")


@dataclass
class CellState:
    """单电芯状态数据结构"""
    cell_id: int
    timestamp: float
    
    # 基本电气参数
    voltage: float              # 端电压 (V)
    current: float              # 电流 (A, 正为充电)
    soc: float                 # 电量状态 (0-1)
    
    # 热参数
    temperature: float          # 电芯温度 (°C)
    heat_generation_rate: float # 产热功率 (W)
    
    # 高级参数 (如果PyBaMM能提供)
    internal_resistance: Optional[float] = None      # 内阻 (Ω)
    ocv: Optional[float] = None                     # 开路电压 (V)
    polarization_voltage: Optional[float] = None    # 极化电压 (V)
    
    # 电化学参数 (高级)
    electrolyte_concentration: Optional[float] = None  # 电解液浓度
    electrode_potential_pos: Optional[float] = None    # 正极电位
    electrode_potential_neg: Optional[float] = None    # 负极电位
    
    # 数据质量
    is_valid: bool = True
    simulation_quality: float = 1.0


class CellSimulator:
    """
    单电芯PyBaMM仿真器
    
    提供基于PyBaMM的高精度单电芯电化学仿真：
    - 支持多种电池化学体系 (LiFePO4, NMC, LCO等)
    - 支持不同复杂度模型 (SPM, SPMe, DFN)
    - 包含热效应建模
    - 支持个体差异参数
    """
    
    def __init__(self, cell_id: int, chemistry: str = "LiFePO4", 
                 model_type: str = "SPM", include_thermal: bool = True):
        """
        初始化单电芯仿真器
        
        Args:
            cell_id: 电芯唯一标识
            chemistry: 电池化学体系
            model_type: 模型类型 (SPM, SPMe, DFN)
            include_thermal: 是否包含热效应
        """
        self.cell_id = cell_id
        self.chemistry = chemistry
        self.model_type = model_type
        self.include_thermal = include_thermal
        
        # PyBaMM组件
        self.pybamm_adapter = None
        self.model = None
        self.parameter_values = None
        self.simulation = None
        self.current_solution = None
        
        # 仿真状态
        self.current_state = None
        self.is_initialized = False
        self.simulation_time = 0.0
        
        # 物理参数缓存
        self.nominal_capacity = 280.0  # Ah
        self.nominal_voltage = 3.2     # V
        self.base_resistance = 0.003   # Ω

        # 初始化OCV参数 (用于备用模型)
        self.ocv_params = {
            'v_min': 2.5, 'v_max': 3.65, 'v_nominal': 3.2,
            'soc_flat_start': 0.1, 'soc_flat_end': 0.9
        }

        # 热模型参数 (用于备用模型)
        self.thermal_params = {
            'thermal_mass': 500.0,
            'thermal_resistance': 2.0,
            'heat_capacity': 1000.0
        }
        
        # 初始化PyBaMM模型
        self._initialize_pybamm_model()

        # 设置默认初始状态 - 修复Bug 1
        self.set_initial_conditions(0.5, 25.0)  # 默认50% SOC, 25°C

        logging.info(f"电芯{cell_id}仿真器初始化: {chemistry} {model_type} "
                    f"{'含热效应' if include_thermal else '不含热效应'}")
    
    def _initialize_pybamm_model(self) -> None:
        """初始化真正的PyBaMM模型"""
        if not PYBAMM_AVAILABLE:
            raise RuntimeError(f"电芯{self.cell_id}: PyBaMM不可用！这是系统的核心依赖")

        try:
            # 创建PyBaMM版本适配器
            self.pybamm_adapter = PyBaMMVersionAdapter()

            # 获取版本信息
            version_info = self.pybamm_adapter.get_version_info()
            if not version_info['adapter_ready']:
                raise RuntimeError("PyBaMM版本适配器初始化失败")

            logging.info(f"电芯{self.cell_id}: 使用PyBaMM {version_info['pybamm_version']}")

            # 创建PyBaMM模型
            self.model = self.pybamm_adapter.create_model(
                model_type=self.model_type,
                include_thermal=self.include_thermal
            )

            # 获取参数集
            self.parameter_values = self.pybamm_adapter.get_parameter_set(self.chemistry)

            # 创建仿真对象
            self.simulation = self.pybamm_adapter.create_simulation(
                self.model,
                self.parameter_values
            )

            self.is_initialized = True
            logging.info(f"电芯{self.cell_id}: 真正的PyBaMM {self.chemistry} {self.model_type}模型初始化成功")

        except Exception as e:
            logging.error(f"电芯{self.cell_id}: PyBaMM模型初始化失败: {e}")
            raise RuntimeError(f"PyBaMM模型初始化失败，这是系统核心功能: {e}")
    
    def _initialize_fallback_model(self) -> None:
        """初始化高精度备用物理模型 (基于PyBaMM原理)"""

        # 根据化学体系调整参数 (基本参数已在__init__中设置)
        if self.chemistry == "NMC":
            self.nominal_voltage = 3.7
            self.base_resistance = 0.002
            self.ocv_params.update({
                'v_min': 2.8, 'v_max': 4.2, 'v_nominal': 3.7,
                'soc_flat_start': 0.2, 'soc_flat_end': 0.8
            })

        self.is_initialized = True
        logging.info(f"电芯{self.cell_id}: 高精度备用物理模型初始化完成 ({self.chemistry})")
    
    def apply_individual_variations(self, variations: Dict[str, float]) -> None:
        """
        应用个体差异参数到PyBaMM模型

        Args:
            variations: 参数变异字典 {'capacity_factor': 1.05, 'resistance_factor': 0.95, ...}
        """
        if not self.is_initialized or not self.parameter_values:
            logging.warning(f"电芯{self.cell_id}: PyBaMM模型未初始化，无法应用个体差异")
            return

        try:
            # 应用容量变异
            if 'capacity_factor' in variations:
                original_capacity = self.parameter_values["Nominal cell capacity [A.h]"]
                new_capacity = original_capacity * variations['capacity_factor']
                self.parameter_values.update({"Nominal cell capacity [A.h]": new_capacity})
                self.nominal_capacity = new_capacity
                logging.debug(f"电芯{self.cell_id}: 容量调整为 {new_capacity:.1f}Ah")

            # 应用电导率变异 (影响内阻)
            if 'resistance_factor' in variations:
                # 通过调整电导率来影响内阻
                try:
                    neg_conductivity = self.parameter_values["Negative electrode conductivity [S.m-1]"]
                    pos_conductivity = self.parameter_values["Positive electrode conductivity [S.m-1]"]

                    # 电导率与电阻成反比
                    resistance_factor = variations['resistance_factor']
                    conductivity_factor = 1.0 / resistance_factor

                    self.parameter_values.update({
                        "Negative electrode conductivity [S.m-1]": neg_conductivity * conductivity_factor,
                        "Positive electrode conductivity [S.m-1]": pos_conductivity * conductivity_factor
                    })
                    logging.debug(f"电芯{self.cell_id}: 电导率调整因子 {conductivity_factor:.3f}")
                except KeyError:
                    logging.debug(f"电芯{self.cell_id}: 电导率参数不可用，跳过电阻变异")

            # 应用热参数变异
            if 'thermal_factor' in variations and self.include_thermal:
                try:
                    thermal_mass = self.parameter_values["Cell thermal mass [J.K-1]"]
                    new_thermal_mass = thermal_mass * variations['thermal_factor']
                    self.parameter_values.update({"Cell thermal mass [J.K-1]": new_thermal_mass})
                    logging.debug(f"电芯{self.cell_id}: 热质量调整为 {new_thermal_mass:.0f}J/K")
                except KeyError:
                    logging.debug(f"电芯{self.cell_id}: 热质量参数不可用，跳过热变异")

            # 重新创建仿真对象以应用新参数
            self.simulation = self.pybamm_adapter.create_simulation(
                self.model,
                self.parameter_values
            )

            # 重新设置初始条件以确保参数变异生效
            if self.current_state:
                current_soc = self.current_state.soc
                current_temp = self.current_state.temperature

                # 重新设置鲁棒的初始条件
                self.parameter_values = self.pybamm_adapter.set_robust_initial_conditions(
                    self.parameter_values, current_soc
                )

                # 重新创建仿真对象
                self.simulation = self.pybamm_adapter.create_simulation(
                    self.model,
                    self.parameter_values
                )

            logging.info(f"电芯{self.cell_id}: PyBaMM个体差异参数应用成功")

        except Exception as e:
            logging.error(f"电芯{self.cell_id}: 应用个体差异参数失败: {e}")
            # 不抛出异常，使用备用模型继续运行
            logging.warning(f"电芯{self.cell_id}: 将使用备用模型继续运行")
    
    def set_initial_conditions(self, initial_soc: float = 0.5,
                             initial_temperature: float = 25.0) -> None:
        """
        设置初始条件 (使用鲁棒的低SOC处理)

        Args:
            initial_soc: 初始SOC (0-1)
            initial_temperature: 初始温度 (°C)
        """
        if not self.is_initialized:
            raise RuntimeError(f"电芯{self.cell_id}仿真器未初始化")

        # 如果使用PyBaMM，设置鲁棒的初始条件
        if self.pybamm_adapter and self.parameter_values:
            try:
                # 使用修复的初始条件设置
                self.parameter_values = self.pybamm_adapter.set_robust_initial_conditions(
                    self.parameter_values, initial_soc
                )

                # 重新创建仿真对象以应用新的初始条件
                self.simulation = self.pybamm_adapter.create_simulation(
                    self.model,
                    self.parameter_values
                )

                logging.info(f"电芯{self.cell_id}: 设置鲁棒PyBaMM初始条件 SOC={initial_soc:.3f}")

            except Exception as e:
                logging.warning(f"电芯{self.cell_id}: PyBaMM初始条件设置失败: {e}")

        # 创建初始状态
        self.current_state = CellState(
            cell_id=self.cell_id,
            timestamp=0.0,
            voltage=self._estimate_initial_voltage(initial_soc),
            current=0.0,
            soc=initial_soc,
            temperature=initial_temperature,
            heat_generation_rate=0.0,
            internal_resistance=self.base_resistance,  # 确保内阻不为None
            ocv=self._calculate_ocv(initial_soc)
        )

        self.simulation_time = 0.0
        
        logging.debug(f"电芯{self.cell_id}: 初始条件设置完成 (SOC={initial_soc}, T={initial_temperature}°C)")
    
    def _estimate_initial_voltage(self, soc: float) -> float:
        """估算初始电压 (基于SOC和化学体系)"""
        return self._calculate_ocv(soc)

    def _calculate_ocv(self, soc: float) -> float:
        """计算开路电压 (基于真实的SOC-OCV关系)"""
        params = self.ocv_params

        if self.chemistry == "LiFePO4":
            # LiFePO4的真实SOC-OCV关系 (平台特性)
            if soc < params['soc_flat_start']:
                # 低SOC区域 - 快速下降
                ratio = soc / params['soc_flat_start']
                return params['v_min'] + (params['v_nominal'] - params['v_min']) * ratio**0.5
            elif soc < params['soc_flat_end']:
                # 平台区域 - 电压相对稳定
                return params['v_nominal'] + 0.05 * np.sin(2 * np.pi * (soc - 0.5))
            else:
                # 高SOC区域 - 快速上升
                ratio = (soc - params['soc_flat_end']) / (1.0 - params['soc_flat_end'])
                return params['v_nominal'] + (params['v_max'] - params['v_nominal']) * ratio**2

        elif self.chemistry == "NMC":
            # NMC的SOC-OCV关系 (更线性)
            return params['v_min'] + (params['v_max'] - params['v_min']) * soc

        else:
            # 默认线性关系
            return params['v_min'] + (params['v_max'] - params['v_min']) * soc
    
    def simulate_step(self, current: float, ambient_temperature: float,
                     dt: float) -> CellState:
        """
        使用真正的PyBaMM进行仿真一个时间步

        Args:
            current: 输入电流 (A, 正为充电)
            ambient_temperature: 环境温度 (°C)
            dt: 时间步长 (s)

        Returns:
            新的电芯状态
        """
        if not self.is_initialized or self.current_state is None:
            raise RuntimeError(f"电芯{self.cell_id}PyBaMM仿真器未正确初始化")

        if not PYBAMM_AVAILABLE or not self.pybamm_adapter:
            raise RuntimeError(f"电芯{self.cell_id}: PyBaMM不可用，无法进行仿真")

        return self._simulate_step_pybamm(current, ambient_temperature, dt)
    
    def _simulate_step_pybamm(self, current: float, ambient_temperature: float,
                            dt: float) -> CellState:
        """使用真正的PyBaMM进行仿真步进"""
        try:
            # 使用PyBaMM版本适配器进行求解
            success, solution = self.pybamm_adapter.solve_step(
                self.simulation,
                current,
                dt,
                initial_soc=self.current_state.soc
            )

            if success and solution:
                # 使用适配器提取结果
                results = self.pybamm_adapter.extract_results(solution)

                if results:
                    # 创建新的电芯状态
                    new_state = CellState(
                        cell_id=self.cell_id,
                        timestamp=self.simulation_time + dt,
                        voltage=results.get('voltage', self.current_state.voltage),
                        current=results.get('current', current),
                        soc=results.get('soc', self.current_state.soc),
                        temperature=results.get('temperature', ambient_temperature),
                        heat_generation_rate=self._calculate_heat_generation_from_pybamm(results, current),
                        internal_resistance=self._estimate_resistance_from_pybamm(results, current),
                        ocv=results.get('ocv', results.get('voltage', self.current_state.voltage)),
                        is_valid=True,
                        simulation_quality=1.0  # 真正的PyBaMM高质量
                    )

                    # 保存当前解用于下一步
                    self.current_solution = solution
                    self.current_state = new_state
                    self.simulation_time += dt

                    logging.debug(f"电芯{self.cell_id}: PyBaMM仿真成功 V={new_state.voltage:.3f}V SOC={new_state.soc:.3f}")
                    return new_state
                else:
                    logging.error(f"电芯{self.cell_id}: PyBaMM结果提取失败")
                    raise RuntimeError("PyBaMM结果提取失败")
            else:
                logging.error(f"电芯{self.cell_id}: PyBaMM求解失败")
                raise RuntimeError("PyBaMM求解失败")

        except Exception as e:
            logging.error(f"电芯{self.cell_id}: PyBaMM仿真步进失败: {e}")
            # 不再使用备用方法，PyBaMM是核心功能
            raise RuntimeError(f"PyBaMM仿真失败，这是系统核心功能: {e}")

    def _calculate_heat_generation_from_pybamm(self, results: Dict[str, float], current: float) -> float:
        """从PyBaMM结果计算产热功率"""
        try:
            voltage = results.get('voltage', 0.0)
            ocv = results.get('ocv', voltage)

            # 基于PyBaMM结果的产热计算
            # 焦耳热 + 极化热
            if voltage > 0 and current != 0:
                power_loss = abs(current * (voltage - ocv))
                return power_loss
            else:
                return 0.0

        except Exception:
            return abs(current) * 0.1  # 简单估算

    def _estimate_resistance_from_pybamm(self, results: Dict[str, float], current: float) -> float:
        """从PyBaMM结果估算内阻"""
        try:
            voltage = results.get('voltage', 0.0)
            ocv = results.get('ocv', voltage)

            if abs(current) > 1e-6:
                resistance = abs(voltage - ocv) / abs(current)
                # 限制内阻范围
                return max(0.001, min(0.1, resistance))
            else:
                return self.base_resistance

        except Exception:
            return self.base_resistance
    
    def _simulate_step_fallback(self, current: float, ambient_temperature: float,
                              dt: float) -> CellState:
        """使用高精度备用物理模型进行仿真步进 (基于电化学原理)"""

        # 1. SOC更新 (库仑计法)
        dsoc = current * dt / (3600 * self.nominal_capacity)
        new_soc = np.clip(self.current_state.soc + dsoc, 0.0, 1.0)

        # 2. 开路电压计算 (基于真实SOC-OCV关系)
        ocv = self._calculate_ocv(new_soc)

        # 3. 内阻计算 (考虑SOC和温度依赖性)
        resistance = self._calculate_resistance(new_soc, self.current_state.temperature)

        # 4. 端电压计算 (考虑欧姆极化和浓差极化)
        ohmic_drop = current * resistance
        concentration_overpotential = self._calculate_concentration_overpotential(current, new_soc)
        new_voltage = ocv - ohmic_drop - concentration_overpotential

        # 5. 热模型 (考虑多种热源)
        heat_generation = self._calculate_heat_generation_detailed(current, new_voltage, ocv)

        # 6. 温度更新 (考虑热容和散热)
        new_temperature = self._update_temperature(
            self.current_state.temperature, heat_generation, ambient_temperature, dt
        )

        # 7. 创建新状态
        new_state = CellState(
            cell_id=self.cell_id,
            timestamp=self.simulation_time + dt,
            voltage=new_voltage,
            current=current,
            soc=new_soc,
            temperature=new_temperature,
            heat_generation_rate=heat_generation,
            internal_resistance=resistance,
            ocv=ocv,
            polarization_voltage=concentration_overpotential,
            is_valid=True,
            simulation_quality=0.95  # 高精度备用模型
        )

        self.current_state = new_state
        self.simulation_time += dt

        return new_state

    def _calculate_resistance(self, soc: float, temperature: float) -> float:
        """计算内阻 (考虑SOC和温度依赖性)"""
        # SOC依赖性 (低SOC和高SOC时内阻增加)
        soc_factor = 1.0 + 0.5 * (1.0 / (soc + 0.1) + 1.0 / (1.1 - soc) - 2.0)

        # 温度依赖性 (低温时内阻增加)
        temp_factor = np.exp(0.02 * (25 - temperature))

        return self.base_resistance * soc_factor * temp_factor

    def _calculate_concentration_overpotential(self, current: float, soc: float) -> float:
        """计算浓差极化电压"""
        # 简化的浓差极化模型
        if abs(current) < 1e-6:
            return 0.0

        # 浓差极化与电流和SOC相关
        soc_factor = 1.0 + 2.0 * abs(soc - 0.5)  # SOC偏离0.5时极化增加
        current_factor = abs(current) / (abs(current) + 10.0)  # 电流饱和效应

        return 0.02 * current_factor * soc_factor * np.sign(current)

    def _calculate_heat_generation_detailed(self, current: float, voltage: float, ocv: float) -> float:
        """详细的产热计算"""
        # 1. 焦耳热 (欧姆损耗)
        resistance = self.current_state.internal_resistance if self.current_state.internal_resistance is not None else self.base_resistance
        joule_heat = current**2 * resistance

        # 2. 极化热 (不可逆热)
        polarization_heat = abs(current) * abs(voltage - ocv) * 0.5

        # 3. 反应热 (可逆热，与温度和SOC相关)
        reaction_heat = current * 0.001 * (self.current_state.temperature - 25)

        return joule_heat + polarization_heat + reaction_heat

    def _update_temperature(self, current_temp: float, heat_generation: float,
                          ambient_temp: float, dt: float) -> float:
        """更新温度 (考虑热容和散热)"""
        thermal_mass = self.thermal_params['thermal_mass']
        thermal_resistance = self.thermal_params['thermal_resistance']

        # 散热
        heat_dissipation = (current_temp - ambient_temp) / thermal_resistance

        # 温度变化率
        dtemp_dt = (heat_generation - heat_dissipation) / thermal_mass

        # 更新温度
        new_temp = current_temp + dtemp_dt * dt

        # 限制温度范围
        return np.clip(new_temp, -20, 80)
    
    def _calculate_heat_generation(self, current: float, voltage: float) -> float:
        """计算产热功率"""
        # 简化的产热计算
        return abs(current) * abs(voltage) * 0.05  # 假设5%的能量转化为热
    
    def get_current_state(self) -> Optional[CellState]:
        """获取当前状态"""
        return self.current_state
    
    def reset(self, initial_soc: float = 0.5, initial_temperature: float = 25.0) -> None:
        """重置仿真器"""
        self.set_initial_conditions(initial_soc, initial_temperature)
        logging.debug(f"电芯{self.cell_id}: 仿真器已重置")
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'cell_id': self.cell_id,
            'chemistry': self.chemistry,
            'model_type': self.model_type,
            'include_thermal': self.include_thermal,
            'pybamm_available': PYBAMM_AVAILABLE,
            'is_initialized': self.is_initialized,
            'simulation_time': self.simulation_time,
            'nominal_capacity': self.nominal_capacity,
            'nominal_voltage': self.nominal_voltage,
            'base_resistance': self.base_resistance
        }

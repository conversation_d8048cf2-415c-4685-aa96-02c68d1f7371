# PyBaMM纯净电池包物理系统配置文件
# 提供基于PyBaMM的高精度电池包物理仿真

# 项目信息
project:
  name: "PyBaMM Pure Battery Pack Physics System"
  version: "2.0.0"
  description: "基于PyBaMM的纯净电池包物理仿真系统，提供真实的电化学数据"
  author: "Battery Algorithm Development Team"
  license: "MIT"

# 仿真基本参数
simulation:
  duration_hours: 168               # 仿真时长(小时) - 一周
  time_step: 1.0                    # 时间步长(秒)
  output_frequency: 1.0             # 输出频率(Hz)
  start_time: "2024-01-01T00:00:00" # 仿真开始时间

# 电池包拓扑配置
pack_topology:
  type: "16s1p_linear"              # 拓扑类型: 16s1p_linear, 16s2p_matrix, custom
  series_count: 16                  # 串联电芯数 (仅custom类型需要)
  parallel_count: 1                 # 并联支路数 (仅custom类型需要)
  layout: "linear"                  # 物理布局: linear, matrix (仅custom类型需要)

# PyBaMM模型配置
pybamm_model:
  chemistry: "LiFePO4"              # 电芯化学体系
  model_type: "SPM"                 # 模型类型: SPM, DFN, SPMe
  thermal_effects: true             # 是否包含热效应
  
  # 电芯参数
  cell_parameters:
    nominal_capacity: 280           # 标称容量(Ah)
    nominal_voltage: 3.2            # 标称电压(V)
    initial_soc: 0.5                # 初始SOC
    initial_temperature: 25         # 初始温度(°C)
  
  # 几何参数
  geometry:
    electrode_width: 0.065          # 电极宽度(m)
    electrode_height: 0.1           # 电极高度(m)
    negative_tab_location: 0.0      # 负极接线片位置
    positive_tab_location: 1.0      # 正极接线片位置

# 充放电模式配置
charge_discharge:
  pattern_type: "sinusoidal"        # 充放电模式: sinusoidal, constant, profile
  
  # 正弦波模式参数
  sinusoidal:
    cycle_duration: 4               # 充放电周期(小时)
    charge_amplitude: 50            # 充电电流幅值(A)
    discharge_amplitude: 80         # 放电电流幅值(A)
    phase_shift: 0                  # 相位偏移(弧度)
    dc_offset: 0                    # 直流偏移(A)
  
  # 恒流模式参数
  constant:
    charge_current: 50              # 恒定充电电流(A)
    discharge_current: -80          # 恒定放电电流(A)
    charge_duration: 2              # 充电时长(小时)
    discharge_duration: 2           # 放电时长(小时)

# 环境条件
environment:
  ambient_temperature: 25           # 环境温度(°C)
  temperature_variation: 5          # 温度变化范围(±°C)
  temperature_cycle_hours: 24       # 温度循环周期(小时)
  
  # 散热条件
  heat_transfer_coefficient: 10     # 传热系数(W/m²/K)
  surface_area: 0.0065             # 散热面积(m²)

# 个体差异配置
individual_variations:
  enable: true
  
  # 参数变异范围
  parameter_variations:
    capacity_std: 0.02              # 容量标准差(相对值)
    resistance_std: 0.05            # 内阻标准差(相对值)
    thermal_std: 0.03               # 热参数标准差(相对值)
    
  # 制造公差
  manufacturing_tolerance:
    electrode_thickness_std: 0.001  # 电极厚度标准差(m)
    porosity_std: 0.01             # 孔隙率标准差
    particle_size_std: 0.1         # 颗粒尺寸标准差(μm)

# 异常注入配置
anomaly_injection:
  enable: true
  
  # 异常场景列表
  scenarios:
    # 连接松动 - 接触电阻增加
    - name: "connection_loose"
      type: "resistance_increase"
      description: "连接松动导致接触电阻增加"
      start_time: 25                # 开始时间(小时)
      target_cells: [3, 7, 11]      # 目标电芯ID
      parameters:
        resistance_multiplier: 2.5   # 电阻倍增因子
        onset_duration: 0.1         # 异常发生持续时间(小时)
        
    # 电芯老化 - 容量和内阻渐变
    - name: "cell_aging"
      type: "gradual_degradation"
      description: "电芯老化导致性能缓慢衰减"
      start_time: 48
      target_cells: [1, 5, 9, 13]
      parameters:
        capacity_fade_rate: 0.001    # 容量衰减率(每小时)
        resistance_growth_rate: 0.002 # 内阻增长率(每小时)
        
    # 过热异常 - 温度异常升高
    - name: "thermal_runaway"
      type: "temperature_spike"
      description: "散热异常导致温度升高"
      start_time: 72
      target_cells: [2, 6]
      parameters:
        temperature_increase: 20     # 温度增加(°C)
        heating_rate: 5.0           # 升温速率(°C/小时)
        
    # 容量衰减 - 可用容量减少
    - name: "capacity_loss"
      type: "capacity_reduction"
      description: "活性物质损失导致容量减少"
      start_time: 96
      target_cells: [4, 8, 12]
      parameters:
        capacity_loss_fraction: 0.15 # 容量损失比例
        
    # 内阻增加 - 电解液干涸等
    - name: "resistance_increase"
      type: "impedance_growth"
      description: "电解液干涸导致内阻增加"
      start_time: 120
      target_cells: [0, 14]
      parameters:
        resistance_increase_factor: 1.8 # 内阻增加因子
        
    # 电压异常 - 过充保护失效
    - name: "overvoltage"
      type: "voltage_anomaly"
      description: "过充保护失效导致电压异常"
      start_time: 144
      target_cells: [10]
      parameters:
        voltage_offset: 0.2          # 电压偏移(V)
        
    # 微短路 - 内部短路
    - name: "micro_short"
      type: "internal_short"
      description: "隔膜缺陷导致微短路"
      start_time: 156
      target_cells: [15]
      parameters:
        short_resistance: 0.1        # 短路电阻(Ω)
        leakage_current: 0.5         # 漏电流(A)

# 数据输出配置
output:
  # 输出变量
  variables:
    - "Terminal voltage [V]"         # 端电压
    - "Current [A]"                  # 电流
    - "Cell temperature [K]"         # 电芯温度
    - "State of charge"              # SOC
    - "Internal resistance [Ohm]"    # 内阻(如果可计算)
    
  # 输出格式
  formats:
    - "csv"                         # CSV格式
    - "json"                        # JSON格式
    - "hdf5"                        # HDF5格式(可选)
    
  # 文件输出
  files:
    raw_data: "simulation_data.csv"
    anomaly_labels: "anomaly_labels.json"
    metadata: "simulation_metadata.json"
    
  # 实时数据流
  real_time:
    enable: true                    # 启用实时数据流
    buffer_size: 1000               # 缓冲区大小
    stream_port: 8888               # 数据流端口

# 验证和测试配置
validation:
  # 数据质量检查
  data_quality:
    voltage_range: [2.0, 4.0]       # 电压有效范围(V)
    current_range: [-200, 200]      # 电流有效范围(A)
    temperature_range: [0, 80]      # 温度有效范围(°C)
    soc_range: [0.0, 1.0]          # SOC有效范围
    
  # 物理约束检查
  physics_constraints:
    max_voltage_change_rate: 0.1    # 最大电压变化率(V/s)
    max_temperature_change_rate: 2.0 # 最大温度变化率(°C/s)
    energy_conservation_tolerance: 0.01 # 能量守恒容差
    
  # 异常检测性能指标
  performance_metrics:
    target_detection_rate: 0.95     # 目标检测率
    max_false_positive_rate: 0.05   # 最大误报率
    max_detection_delay: 60         # 最大检测延迟(秒)

# 日志和调试配置
logging:
  level: "INFO"                     # 日志级别: DEBUG, INFO, WARNING, ERROR
  file: "pybamm_virtual_battery.log"
  max_file_size: "10MB"
  backup_count: 5
  
  # 调试选项
  debug:
    save_intermediate_results: false # 保存中间结果
    plot_real_time: false           # 实时绘图
    verbose_anomaly_injection: true  # 详细异常注入日志

# 性能优化配置
performance:
  # 并行计算
  parallel:
    enable: true                    # 启用并行计算
    num_processes: 4                # 进程数量
    
  # 内存管理
  memory:
    max_memory_usage: "2GB"         # 最大内存使用
    garbage_collection_interval: 100 # 垃圾回收间隔
    
  # 计算优化
  computation:
    solver_tolerance: 1e-6          # 求解器容差
    max_solver_iterations: 1000     # 最大求解器迭代次数
    adaptive_time_stepping: true    # 自适应时间步长

# 扩展接口配置
interfaces:
  # 标准数据接口
  standard_interface:
    enable: true
    data_format: "sensor_data"      # 数据格式标准
    
  # REST API接口
  rest_api:
    enable: false                   # 启用REST API
    host: "localhost"
    port: 5000
    
  # WebSocket接口
  websocket:
    enable: false                   # 启用WebSocket
    port: 8080
    
  # 文件接口
  file_interface:
    enable: true
    watch_directory: "input"        # 监控输入目录
    output_directory: "output"      # 输出目录

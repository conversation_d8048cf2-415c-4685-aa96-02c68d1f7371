# PyBaMM Virtual Battery System

🔋 **基于PyBaMM 25.6.0的高精度虚拟电池包系统**

[![Python](https://img.shields.io/badge/Python-3.8%2B-blue.svg)](https://python.org)
[![PyBaMM](https://img.shields.io/badge/PyBaMM-25.6.0-green.svg)](https://pybamm.org)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

一个完整的、企业级的虚拟电池包仿真系统，集成了真正的PyBaMM物理引擎和完整的BMS保护系统，专为电池异常检测算法开发和验证而设计。

## ✨ 核心特性

### 🚀 **真正的PyBaMM物理引擎**
- **PyBaMM 25.6.0** + **IDAKLUSolver** 最新求解器
- **SPM电化学模型** 提供高精度物理仿真
- **全SOC范围支持** (5%-95%) 包括鲁棒的低SOC处理
- **热耦合模型** 真实的温度效应仿真

### 🛡️ **企业级BMS保护系统**
- **5项完整保护功能**: 过充/欠压/温度/电流/均衡保护
- **智能电流降额**: 根据温度和SOC动态调整
- **多级保护机制**: 警告→保护→故障的渐进式保护
- **实时状态监控**: 完整的保护事件记录和历史

### 🔧 **灵活的系统架构**
- **多种拓扑支持**: 16S1P, 8S2P, 4S4P等配置
- **个体差异建模**: 电芯间容量、内阻、热特性差异
- **热耦合网络**: 电芯间热传导和环境热交换
- **高性能仿真**: 1.6x实时倍数，适合实时应用

## 📁 系统架构

```
pybamm_virtual_battery/
├── 📦 核心仿真模块
│   ├── pack_simulator.py      # 电池包仿真器 (主入口)
│   ├── cell_simulator.py      # 单电芯PyBaMM仿真器
│   └── pack_topology.py       # 电池包拓扑定义
├── 🔌 PyBaMM集成
│   └── pybamm_adapter.py      # PyBaMM 25.6.0版本适配器
├── 🛡️ 保护系统
│   └── bms.py                 # 完整BMS保护系统
├── 📊 测试和文档
│   ├── README.md              # 本文档
│   └── test_*.py              # 完整测试套件
└── 🔧 配置文件
    └── requirements.txt       # 依赖包列表
```

## 🚀 快速开始

### 1. 环境要求

```bash
# Python环境
Python 3.8+

# 核心依赖
PyBaMM >= 25.6.0
NumPy >= 1.21.0
SciPy >= 1.7.0
```

### 2. 安装依赖

```bash
# 安装PyBaMM (推荐使用conda)
conda install -c conda-forge pybamm

# 或使用pip
pip install pybamm>=25.6.0

# 安装其他依赖
pip install numpy scipy
```

### 3. 基本使用示例

#### 🔋 **创建16S1P电池包**

```python
from pack_simulator import BatteryPackSimulator
from pack_topology import StandardPackTopologies

# 创建16S1P LiFePO4电池包
topology = StandardPackTopologies.create_16s1p_linear()
pack_sim = BatteryPackSimulator(
    topology=topology, 
    chemistry="LiFePO4",
    include_thermal=True  # 启用热模型
)

# 设置初始条件
pack_sim.set_initial_conditions(
    initial_soc=0.5,        # 50% SOC
    initial_temperature=25.0 # 25°C
)

print("✅ 16S1P电池包创建成功")
```

#### ⚡ **运行仿真步骤**

```python
# 运行充电仿真
pack_state = pack_sim.simulate_pack_step(
    pack_current=50.0,           # 50A充电
    ambient_temperature=25.0,    # 环境温度25°C
    dt=10.0                      # 10秒步长
)

# 查看结果
print(f"包电压: {pack_state.pack_voltage:.2f}V")
print(f"包SOC: {pack_state.pack_soc:.3f}")
print(f"包温度: {pack_state.pack_temperature:.1f}°C")
print(f"BMS状态: {pack_state.bms_status.system_status.value}")
print(f"实际电流: {pack_state.actual_current:.1f}A")
```

#### 🛡️ **BMS保护系统**

```python
# BMS会自动进行保护检查
if pack_state.bms_protection_active:
    protection_summary = pack_sim.bms.get_protection_summary()
    print("🚨 BMS保护激活:")
    for protection in protection_summary['protections']:
        print(f"   - {protection['description']}")
        print(f"   - 动作: {protection['action']}")
```

## 📚 详细功能说明

### 🔋 **电池包仿真器 (BatteryPackSimulator)**

电池包仿真器是系统的核心组件，提供完整的电池包级仿真功能。

#### **主要方法**

```python
# 创建仿真器
pack_sim = BatteryPackSimulator(
    topology,                    # 电池包拓扑
    chemistry="LiFePO4",        # 化学体系
    model_type="SPM",           # 模型类型
    include_thermal=True        # 是否包含热模型
)

# 设置初始条件
pack_sim.set_initial_conditions(initial_soc, initial_temperature)

# 应用个体差异
variations_list = [
    {'capacity_factor': 1.05, 'resistance_factor': 0.95},
    {'capacity_factor': 0.98, 'resistance_factor': 1.02},
    # ... 为每个电芯设置差异
]
pack_sim.apply_individual_variations(variations_list)

# 运行仿真步骤
pack_state = pack_sim.simulate_pack_step(current, temperature, dt)
```

#### **返回的包状态 (PackState)**

```python
pack_state.pack_voltage          # 包总电压 (V)
pack_state.pack_current          # 包电流 (A)
pack_state.pack_soc             # 包平均SOC (0-1)
pack_state.pack_temperature     # 包平均温度 (°C)
pack_state.cell_states          # 各电芯详细状态列表
pack_state.bms_status           # BMS状态信息
pack_state.actual_current       # BMS限制后的实际电流
pack_state.simulation_quality   # 仿真质量 (1.0=真正PyBaMM)
```

### 🏗️ **电池包拓扑 (PackTopology)**

支持多种标准电池包配置，可扩展自定义拓扑。

#### **标准拓扑**

```python
from pack_topology import StandardPackTopologies

# 16串1并 (线性排列)
topology_16s1p = StandardPackTopologies.create_16s1p_linear()

# 8串2并 (矩阵排列)
topology_8s2p = StandardPackTopologies.create_8s2p_matrix()

# 4串4并 (矩阵排列)
topology_4s4p = StandardPackTopologies.create_4s4p_matrix()

# 验证拓扑
validation = topology.validate_topology()
if validation['is_valid']:
    print(f"✅ 拓扑验证成功: {topology.total_cells}个电芯")
else:
    print(f"❌ 拓扑验证失败: {validation['errors']}")
```

#### **拓扑信息**

```python
topology.total_cells            # 总电芯数
topology.series_count          # 串联数
topology.parallel_count        # 并联数
topology.electrical_connections # 电气连接
topology.thermal_connections   # 热连接
topology.cell_positions        # 电芯物理位置
```

### 🛡️ **BMS保护系统 (BatteryManagementSystem)**

完整的电池管理系统，提供企业级的安全保护功能。

#### **保护功能**

```python
from bms import BatteryManagementSystem, BMSProtectionLevel

# 创建BMS系统
bms = BatteryManagementSystem(chemistry="LiFePO4", cell_count=16)

# 获取系统信息
bms_info = bms.get_system_info()
print(f"BMS系统: {bms_info['chemistry']}, {bms_info['cell_count']}个电芯")

# 保护状态检查
allowed, actual_current, bms_status = bms.update_status(pack_state, requested_current)

# 保护事件摘要
protection_summary = bms.get_protection_summary()
```

#### **保护参数 (LiFePO4)**

| 保护类型 | 警告阈值 | 保护阈值 | 动作 |
|---------|---------|---------|------|
| 过压保护 | 3.65V | 3.70V | 禁止充电 |
| 欠压保护 | 2.50V | 2.40V | 禁止放电 |
| 过温保护 | 55°C | 60°C | 禁止充放电 |
| 低温保护 | -10°C | -20°C | 禁止充电 |
| 充电过流 | - | 100A | 电流限制 |
| 放电过流 | - | 200A | 电流限制 |
| SOC过高 | 95% | 98% | 禁止充电 |
| SOC过低 | 5% | 2% | 禁止放电 |

### 🔌 **PyBaMM适配器 (PyBaMMVersionAdapter)**

处理PyBaMM版本兼容性和参数适配的核心组件。

#### **主要功能**

```python
from pybamm_adapter import PyBaMMVersionAdapter

# 创建适配器
adapter = PyBaMMVersionAdapter()

# 获取版本信息
version_info = adapter.get_version_info()
print(f"PyBaMM版本: {version_info['pybamm_version']}")
print(f"适配器就绪: {version_info['adapter_ready']}")

# 获取参数集
params = adapter.get_parameter_set("LiFePO4")

# 创建模型
model = adapter.create_model("SPM", include_thermal=True)

# 创建仿真
simulation = adapter.create_simulation(model, params)

# 设置鲁棒初始条件 (支持低SOC)
robust_params = adapter.set_robust_initial_conditions(params, target_soc=0.05)
```

## 🧪 测试和验证

### 运行完整测试套件

```bash
# 运行完整系统测试
python test_pybamm_virtual_battery_system.py

# 运行BMS保护测试
python test_bms_improved.py

# 运行欠压保护修复测试
python test_undervoltage_fix.py
```

### 测试结果

```
✅ PyBaMM适配器: 通过
✅ 单电芯仿真: 通过  
✅ 电池包拓扑: 通过
✅ BMS保护系统: 通过
✅ 电池包仿真: 通过
✅ 系统性能: 通过

总体结果: 6/6 测试通过
```

## 🎯 应用场景

### 1. **电池异常检测算法开发**

```python
# 为异常检测算法提供高质量训练数据
for scenario in test_scenarios:
    pack_state = pack_sim.simulate_pack_step(
        scenario['current'], 
        scenario['temperature'], 
        scenario['duration']
    )
    
    # 提取特征用于机器学习
    features = extract_features(pack_state)
    labels = get_ground_truth_labels(pack_state)
```

### 2. **BMS算法验证**

```python
# 验证BMS保护逻辑
test_conditions = [
    {'soc': 0.05, 'current': -100.0},  # 低SOC大电流放电
    {'soc': 0.95, 'current': 100.0},   # 高SOC大电流充电
    {'temp': 65.0, 'current': 50.0},   # 高温充电
]

for condition in test_conditions:
    # 测试BMS响应
    pack_sim.set_initial_conditions(condition['soc'], condition.get('temp', 25.0))
    pack_state = pack_sim.simulate_pack_step(condition['current'], 25.0, 10.0)
    
    # 验证保护是否正确触发
    assert pack_state.bms_protection_active
```

### 3. **电池包设计优化**

```python
# 比较不同拓扑的性能
topologies = [
    StandardPackTopologies.create_16s1p_linear(),
    StandardPackTopologies.create_8s2p_matrix(),
    StandardPackTopologies.create_4s4p_matrix(),
]

for topology in topologies:
    pack_sim = BatteryPackSimulator(topology, "LiFePO4")
    # 运行标准测试并比较结果
    performance_metrics = run_performance_test(pack_sim)
```

## 🔧 高级配置

### 个体差异建模

```python
# 为每个电芯设置不同的特性
variations_list = []
for i in range(16):
    variations = {
        'capacity_factor': np.random.normal(1.0, 0.02),    # 2%容量差异
        'resistance_factor': np.random.normal(1.0, 0.01),  # 1%内阻差异
        'thermal_factor': np.random.normal(1.0, 0.005),    # 0.5%热差异
    }
    variations_list.append(variations)

pack_sim.apply_individual_variations(variations_list)
```

### 自定义BMS参数

```python
# 调整BMS保护参数
bms.protection_params.update({
    'cell_overvoltage_protection': 3.75,  # 更严格的过压保护
    'max_charge_current': 80.0,           # 降低最大充电电流
    'overtemperature_warning': 50.0,      # 更低的温度警告
})
```

## 📈 性能特性

- **仿真速度**: 1.6x实时倍数 (16个电芯，1秒步长)
- **内存使用**: < 100MB (典型16S1P配置)
- **精度**: PyBaMM电化学模型，4位小数精度
- **稳定性**: 支持5%-95% SOC范围，包括极端工况
- **可扩展性**: 支持最多64个电芯的电池包配置


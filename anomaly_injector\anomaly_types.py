"""
异常类型定义

定义各种电池异常的类型、参数和物理实现方法。
每种异常都对应特定的PyBaMM参数修改策略。
"""

import numpy as np
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List
from enum import Enum


class AnomalyCategory(Enum):
    """异常类别"""
    ELECTRICAL = "electrical"      # 电气异常
    THERMAL = "thermal"           # 热异常
    MECHANICAL = "mechanical"     # 机械异常
    CHEMICAL = "chemical"         # 化学异常


class AnomalySeverity(Enum):
    """异常严重程度"""
    MILD = "mild"           # 轻微 (5-15%变化)
    MODERATE = "moderate"   # 中等 (15-30%变化)
    SEVERE = "severe"       # 严重 (30-50%变化)
    CRITICAL = "critical"   # 危急 (>50%变化)


@dataclass
class AnomalyType(ABC):
    """异常类型基类"""
    name: str
    category: AnomalyCategory
    severity: AnomalySeverity
    description: str
    target_layer: int  # 目标诊断层 (1=内阻, 2=容量, 3=安全)
    
    # 异常参数
    parameters: Dict[str, Any] = field(default_factory=dict)
    
    # 时间特性
    onset_time: float = 0.0      # 异常开始时间 (s)
    duration: Optional[float] = None  # 持续时间 (s), None表示永久
    
    # 渐变特性
    is_gradual: bool = False     # 是否渐变
    ramp_time: float = 0.0       # 渐变时间 (s)
    
    @abstractmethod
    def get_pybamm_parameter_changes(self) -> Dict[str, Any]:
        """
        获取需要修改的PyBaMM参数
        
        Returns:
            Dict[str, Any]: 参数名到新值的映射
        """
        pass
    
    @abstractmethod
    def get_fallback_modifications(self) -> Dict[str, Any]:
        """
        获取备用模型的修改参数
        
        Returns:
            Dict[str, Any]: 备用模型参数修改
        """
        pass
    
    def calculate_current_severity(self, current_time: float) -> float:
        """
        计算当前时刻的异常严重程度
        
        Args:
            current_time: 当前时间 (s)
            
        Returns:
            float: 严重程度因子 (0.0-1.0)
        """
        if current_time < self.onset_time:
            return 0.0
        
        if self.duration is not None and current_time > self.onset_time + self.duration:
            return 0.0
        
        elapsed = current_time - self.onset_time
        
        if self.is_gradual and elapsed < self.ramp_time:
            # 渐变阶段
            return elapsed / self.ramp_time
        else:
            # 稳定阶段
            return 1.0


@dataclass
class ResistanceAnomaly(AnomalyType):
    """
    内阻异常 - 测试第一层诊断
    
    模拟连接器松动、腐蚀或电芯老化导致的内阻增加
    """
    
    def __init__(self, severity: AnomalySeverity = AnomalySeverity.MODERATE,
                 resistance_multiplier: Optional[float] = None,
                 **kwargs):
        
        # 根据严重程度设置默认参数
        if resistance_multiplier is None:
            multiplier_map = {
                AnomalySeverity.MILD: 1.1,      # 10%增加
                AnomalySeverity.MODERATE: 1.3,   # 30%增加
                AnomalySeverity.SEVERE: 1.8,     # 80%增加
                AnomalySeverity.CRITICAL: 3.0    # 200%增加
            }
            resistance_multiplier = multiplier_map[severity]
        
        super().__init__(
            name="resistance_anomaly",
            category=AnomalyCategory.ELECTRICAL,
            severity=severity,
            description=f"内阻增加{(resistance_multiplier-1)*100:.0f}%",
            target_layer=1,
            parameters={'resistance_multiplier': resistance_multiplier},
            **kwargs
        )
    
    def get_pybamm_parameter_changes(self) -> Dict[str, Any]:
        """修改PyBaMM内阻相关参数"""
        multiplier = self.parameters['resistance_multiplier']
        current_factor = self.calculate_current_severity(0.0)  # 将在运行时更新
        effective_multiplier = 1.0 + (multiplier - 1.0) * current_factor
        
        # 使用对SPM模型敏感的参数来模拟内阻增加
        # 内阻增加 = 交换电流密度降低 + 扩散系数降低

        # 使用更大的参数变化幅度，确保超过PyBaMM数值求解器的敏感度阈值
        # 注意：这里返回的是相对乘数，不是绝对值

        # 使用更大的变化幅度
        large_multiplier = 1.0 + (effective_multiplier - 1.0) * 2.0  # 放大2倍

        return {
            # 主要修改：大幅降低负极交换电流密度
            "Negative electrode exchange-current density [A.m-2]": 1.0 / large_multiplier,

            # 中等修改：正极交换电流密度
            "Positive electrode exchange-current density [A.m-2]": 1.0 / (1.0 + (large_multiplier-1.0)*0.5),

            # 轻微修改：扩散系数
            "Negative particle diffusivity [m2.s-1]": 1.0 / (1.0 + (large_multiplier-1.0)*0.3),
        }
    
    def get_fallback_modifications(self) -> Dict[str, Any]:
        """修改备用模型的内阻参数"""
        multiplier = self.parameters['resistance_multiplier']
        current_factor = self.calculate_current_severity(0.0)
        effective_multiplier = 1.0 + (multiplier - 1.0) * current_factor
        
        return {
            'base_resistance_multiplier': effective_multiplier
        }


@dataclass
class CapacityAnomaly(AnomalyType):
    """
    容量衰减异常 - 测试第二层诊断
    
    模拟活性物质损失(LAM)或可循环锂离子损失(LLI)
    """
    
    def __init__(self, severity: AnomalySeverity = AnomalySeverity.MODERATE,
                 capacity_fade_factor: Optional[float] = None,
                 **kwargs):
        
        # 根据严重程度设置默认参数
        if capacity_fade_factor is None:
            fade_map = {
                AnomalySeverity.MILD: 0.95,      # 5%衰减
                AnomalySeverity.MODERATE: 0.85,   # 15%衰减
                AnomalySeverity.SEVERE: 0.70,     # 30%衰减
                AnomalySeverity.CRITICAL: 0.50    # 50%衰减
            }
            capacity_fade_factor = fade_map[severity]
        
        super().__init__(
            name="capacity_anomaly",
            category=AnomalyCategory.CHEMICAL,
            severity=severity,
            description=f"容量衰减{(1-capacity_fade_factor)*100:.0f}%",
            target_layer=2,
            parameters={'capacity_fade_factor': capacity_fade_factor},
            **kwargs
        )
    
    def get_pybamm_parameter_changes(self) -> Dict[str, Any]:
        """修改PyBaMM容量相关参数"""
        fade_factor = self.parameters['capacity_fade_factor']
        # 对于容量异常，一旦注入就立即生效，不使用渐变
        # 直接使用完整的严重程度因子
        current_factor = 1.0
        effective_fade = 1.0 - (1.0 - fade_factor) * current_factor
        
        # 使用更敏感的参数：直接修改电极厚度（对电压影响更大）
        # 容量衰减 = 电极厚度减少（模拟活性物质损失）

        return {
            # 主要修改：电极厚度（对电压影响更敏感）
            "Negative electrode thickness [m]": 88e-6 * effective_fade,  # 从88μm减少
            "Positive electrode thickness [m]": 80e-6 * effective_fade,  # 从80μm减少

            # 备选：轻微修改活性物质体积分数
            "Negative electrode active material volume fraction": 0.75 * (1.0 - (1.0 - effective_fade) * 0.1),
        }
    
    def get_fallback_modifications(self) -> Dict[str, Any]:
        """修改备用模型的容量参数"""
        fade_factor = self.parameters['capacity_fade_factor']
        current_factor = self.calculate_current_severity(0.0)
        effective_fade = 1.0 - (1.0 - fade_factor) * current_factor
        
        return {
            'nominal_capacity_multiplier': effective_fade
        }


@dataclass
class ShortCircuitAnomaly(AnomalyType):
    """
    内部短路异常 - 测试第三层诊断
    
    模拟内部微短路导致的持续自放电和额外产热
    """
    
    def __init__(self, severity: AnomalySeverity = AnomalySeverity.MODERATE,
                 leakage_current: Optional[float] = None,
                 **kwargs):
        
        # 根据严重程度设置默认参数
        if leakage_current is None:
            current_map = {
                AnomalySeverity.MILD: 0.05,      # 50mA漏电流
                AnomalySeverity.MODERATE: 0.2,    # 200mA漏电流
                AnomalySeverity.SEVERE: 0.5,      # 500mA漏电流
                AnomalySeverity.CRITICAL: 1.0     # 1A漏电流
            }
            leakage_current = current_map[severity]
        
        super().__init__(
            name="short_circuit_anomaly",
            category=AnomalyCategory.ELECTRICAL,
            severity=severity,
            description=f"内部短路，漏电流{leakage_current*1000:.0f}mA",
            target_layer=3,
            parameters={'leakage_current': leakage_current},
            **kwargs
        )
    
    def get_pybamm_parameter_changes(self) -> Dict[str, Any]:
        """
        内部短路比较复杂，主要通过修改电流输入来实现
        这里返回空字典，实际实现在EnhancedCellSimulator中
        """
        return {}
    
    def get_fallback_modifications(self) -> Dict[str, Any]:
        """修改备用模型以模拟短路效应"""
        leakage = self.parameters['leakage_current']
        current_factor = self.calculate_current_severity(0.0)
        effective_leakage = leakage * current_factor
        
        return {
            'leakage_current': effective_leakage,
            'additional_heat_generation': effective_leakage * 0.1  # 额外产热
        }


@dataclass
class ThermalAnomaly(AnomalyType):
    """
    热异常 - 测试热管理和安全保护
    
    模拟散热不良或额外热源
    """
    
    def __init__(self, severity: AnomalySeverity = AnomalySeverity.MODERATE,
                 thermal_resistance_multiplier: Optional[float] = None,
                 additional_heat_source: Optional[float] = None,
                 **kwargs):
        
        # 根据严重程度设置默认参数
        if thermal_resistance_multiplier is None:
            resistance_map = {
                AnomalySeverity.MILD: 1.2,       # 20%散热恶化
                AnomalySeverity.MODERATE: 1.5,    # 50%散热恶化
                AnomalySeverity.SEVERE: 2.0,      # 100%散热恶化
                AnomalySeverity.CRITICAL: 3.0     # 200%散热恶化
            }
            thermal_resistance_multiplier = resistance_map[severity]
        
        if additional_heat_source is None:
            heat_map = {
                AnomalySeverity.MILD: 0.5,       # 0.5W额外热源
                AnomalySeverity.MODERATE: 1.0,    # 1W额外热源
                AnomalySeverity.SEVERE: 2.0,      # 2W额外热源
                AnomalySeverity.CRITICAL: 5.0     # 5W额外热源
            }
            additional_heat_source = heat_map[severity]
        
        super().__init__(
            name="thermal_anomaly",
            category=AnomalyCategory.THERMAL,
            severity=severity,
            description=f"热异常：散热恶化{(thermal_resistance_multiplier-1)*100:.0f}%，额外热源{additional_heat_source}W",
            target_layer=3,
            parameters={
                'thermal_resistance_multiplier': thermal_resistance_multiplier,
                'additional_heat_source': additional_heat_source
            },
            **kwargs
        )
    
    def get_pybamm_parameter_changes(self) -> Dict[str, Any]:
        """修改PyBaMM热相关参数"""
        thermal_mult = self.parameters['thermal_resistance_multiplier']
        current_factor = self.calculate_current_severity(0.0)
        effective_mult = 1.0 + (thermal_mult - 1.0) * current_factor

        # 使用PyBaMM实际存在的热参数（基于参数调研结果）
        return {
            # 降低电极热传导系数（模拟散热恶化）
            "Negative electrode thermal conductivity [W.m-1.K-1]": 1.7 / (effective_mult * 1.5),
            "Positive electrode thermal conductivity [W.m-1.K-1]": 2.1 / (effective_mult * 1.5),
            "Separator thermal conductivity [W.m-1.K-1]": 0.16 / (effective_mult * 1.5),

            # 降低传热系数（模拟散热能力下降）
            "Total heat transfer coefficient [W.m-2.K-1]": 10.0 / (effective_mult * 2.0),

            # 增加环境温度（模拟高温环境）
            "Ambient temperature [K]": 298.15 + (effective_mult - 1.0) * 10.0,

            # 增加内阻以产生更多热量
            "Negative electrode exchange-current density [A.m-2]": 1.0 / effective_mult,
        }
    
    def get_fallback_modifications(self) -> Dict[str, Any]:
        """修改备用模型的热参数"""
        thermal_mult = self.parameters['thermal_resistance_multiplier']
        heat_source = self.parameters['additional_heat_source']
        current_factor = self.calculate_current_severity(0.0)
        
        return {
            'thermal_resistance_multiplier': 1.0 + (thermal_mult - 1.0) * current_factor,
            'additional_heat_generation': heat_source * current_factor
        }

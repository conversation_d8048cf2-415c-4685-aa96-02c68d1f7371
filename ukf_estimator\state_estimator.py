"""
电池状态估算器主类

集成UKF核心算法和电池模型，提供完整的SOC和内阻估算功能。
与虚拟电池系统紧密集成，支持实时状态估算。
"""

import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, field
import logging
import time

from .ukf_core import UKFCore, UKFConfig
from .battery_models import FirstOrderRCModel, BatteryParameters
from .data_interface import VirtualBatteryInterface, DataBuffer


@dataclass
class EstimationResult:
    """单次估算结果"""
    timestamp: float
    
    # 估算的状态
    soc: float                    # 电量状态 (0-1)
    soc_uncertainty: float        # SOC不确定性 (标准差)
    
    # 估算的参数
    r0: float                     # 欧姆内阻 (Ω)
    r1: float                     # 极化内阻 (Ω)
    c1: float                     # 极化电容 (F)
    v1: float                     # 极化电压 (V)
    
    # 参数不确定性
    r0_uncertainty: float         # R0不确定性
    r1_uncertainty: float         # R1不确定性
    c1_uncertainty: float         # C1不确定性
    
    # 观测数据
    measured_voltage: float       # 测量电压 (V)
    measured_current: float       # 测量电流 (A)
    temperature: float            # 温度 (°C)
    
    # 模型预测
    predicted_voltage: float      # 模型预测电压 (V)
    voltage_error: float          # 电压误差 (V)
    
    # 质量指标
    innovation: float             # 新息 (测量值-预测值)
    likelihood: float             # 似然度
    is_valid: bool = True         # 估算是否有效


class BatteryStateEstimator:
    """
    电池状态估算器
    
    集成UKF算法和一阶RC电池模型，提供SOC和内阻的实时估算。
    支持与虚拟电池系统的数据接口，可用于算法验证和性能评估。
    """
    
    def __init__(self, battery_model: FirstOrderRCModel, ukf_core: UKFCore,
                 data_interface: Optional[VirtualBatteryInterface] = None):
        """
        初始化状态估算器
        
        Args:
            battery_model: 电池等效电路模型
            ukf_core: UKF核心算法
            data_interface: 数据接口 (可选)
        """
        self.battery_model = battery_model
        self.ukf_core = ukf_core
        self.data_interface = data_interface
        
        # 估算状态
        self.is_initialized = False
        self.current_temperature = 25.0
        self.current_current = 0.0
        
        # 历史记录
        self.estimation_history: List[EstimationResult] = []
        self.data_buffer = DataBuffer(max_size=1000)
        
        # 性能统计
        self.total_estimations = 0
        self.successful_estimations = 0
        self.average_voltage_error = 0.0
        
        logging.info("电池状态估算器初始化完成")
    
    def initialize(self, initial_soc: float, initial_temperature: float = 25.0,
                   initial_covariance_scale: float = 1.0):
        """
        初始化估算器
        
        Args:
            initial_soc: 初始SOC (0-1)
            initial_temperature: 初始温度 (°C)
            initial_covariance_scale: 初始协方差缩放因子
        """
        # 获取初始状态和协方差
        initial_state = self.battery_model.get_initial_state(initial_soc, initial_temperature)
        initial_covariance = self.battery_model.get_initial_covariance() * initial_covariance_scale
        
        # 构建过程噪声协方差矩阵
        process_noise_cov = self._build_process_noise_covariance()
        
        # 测量噪声方差
        measurement_noise_var = self.ukf_core.config.measurement_noise_std**2
        
        # 初始化UKF
        self.ukf_core.initialize(
            initial_state=initial_state,
            initial_covariance=initial_covariance,
            process_noise_cov=process_noise_cov,
            measurement_noise_var=measurement_noise_var
        )
        
        self.current_temperature = initial_temperature
        self.is_initialized = True
        
        logging.info(f"状态估算器初始化: SOC={initial_soc:.3f}, T={initial_temperature:.1f}°C")
    
    def _build_process_noise_covariance(self) -> np.ndarray:
        """构建过程噪声协方差矩阵"""
        config = self.ukf_core.config
        
        if self.battery_model.estimate_parameters:
            # 5维状态: [SOC, V1, R0, R1, C1]
            noise_stds = [
                config.process_noise_std['soc'],
                config.process_noise_std['v1'],
                config.process_noise_std['r0'],
                config.process_noise_std['r1'],
                config.process_noise_std['c1']
            ]
        else:
            # 2维状态: [SOC, V1]
            noise_stds = [
                config.process_noise_std['soc'],
                config.process_noise_std['v1']
            ]
        
        return np.diag([std**2 for std in noise_stds])
    
    def estimate_step(self, voltage: float, current: float, temperature: float, dt: float) -> EstimationResult:
        """
        执行一步状态估算
        
        Args:
            voltage: 测量电压 (V)
            current: 测量电流 (A, 正为充电)
            temperature: 温度 (°C)
            dt: 时间步长 (s)
            
        Returns:
            估算结果
        """
        if not self.is_initialized:
            raise RuntimeError("估算器未初始化，请先调用initialize()方法")
        
        timestamp = time.time()
        self.current_temperature = temperature
        self.current_current = current
        
        try:
            # 1. UKF预测步骤
            def state_transition_func(x, dt_inner, u):
                return self.battery_model.state_transition(x, dt_inner, current, temperature)
            
            x_pred, P_pred = self.ukf_core.predict(state_transition_func, dt)
            
            # 约束状态
            x_pred = self.battery_model.validate_state(x_pred)
            
            # 2. UKF更新步骤
            def observation_func(x):
                return self.battery_model.observation_with_current(x, current, temperature)
            
            x_est, P_est = self.ukf_core.update(voltage, observation_func)
            
            # 约束状态
            x_est = self.battery_model.validate_state(x_est)
            
            # 3. 提取估算结果
            params = self.battery_model.extract_parameters(x_est)
            uncertainties = np.sqrt(np.diag(P_est))
            
            # 4. 计算模型预测和误差
            predicted_voltage = observation_func(x_est)
            voltage_error = voltage - predicted_voltage
            innovation = voltage_error
            
            # 5. 计算似然度 (简化版本)
            likelihood = np.exp(-0.5 * (innovation / self.ukf_core.config.measurement_noise_std)**2)
            
            # 6. 创建估算结果
            result = EstimationResult(
                timestamp=timestamp,
                soc=params['soc'],
                soc_uncertainty=uncertainties[0],
                r0=params['r0'],
                r1=params['r1'],
                c1=params['c1'],
                v1=params['v1'],
                r0_uncertainty=uncertainties[2] if len(uncertainties) > 2 else 0.0,
                r1_uncertainty=uncertainties[3] if len(uncertainties) > 3 else 0.0,
                c1_uncertainty=uncertainties[4] if len(uncertainties) > 4 else 0.0,
                measured_voltage=voltage,
                measured_current=current,
                temperature=temperature,
                predicted_voltage=predicted_voltage,
                voltage_error=voltage_error,
                innovation=innovation,
                likelihood=likelihood,
                is_valid=True
            )
            
            # 7. 更新统计信息
            self._update_statistics(result)
            
            # 8. 保存历史记录
            self.estimation_history.append(result)
            self.data_buffer.add_data({
                'timestamp': timestamp,
                'voltage': voltage,
                'current': current,
                'temperature': temperature,
                'soc_est': params['soc'],
                'r0_est': params['r0'],
                'r1_est': params['r1']
            })
            
            logging.debug(f"估算完成: SOC={params['soc']:.3f}, R0={params['r0']:.4f}Ω, "
                         f"R1={params['r1']:.4f}Ω, 电压误差={voltage_error:.3f}mV")
            
            return result
            
        except Exception as e:
            logging.error(f"State estimation failed: {e}")

            # 尝试重置UKF状态
            try:
                self.ukf_core.reset()
                initial_state = self.battery_model.get_initial_state(0.5, temperature)
                initial_covariance = self.battery_model.get_initial_covariance() * 10.0
                process_noise_cov = self._build_process_noise_covariance()
                measurement_noise_var = self.ukf_core.config.measurement_noise_std**2

                self.ukf_core.initialize(
                    initial_state=initial_state,
                    initial_covariance=initial_covariance,
                    process_noise_cov=process_noise_cov,
                    measurement_noise_var=measurement_noise_var
                )
                logging.info("UKF state reset successfully")
            except:
                pass

            # 返回失效结果
            return EstimationResult(
                timestamp=timestamp,
                soc=0.5, soc_uncertainty=1.0,
                r0=0.003, r1=0.002, c1=3000.0, v1=0.0,
                r0_uncertainty=1.0, r1_uncertainty=1.0, c1_uncertainty=1000.0,
                measured_voltage=voltage, measured_current=current, temperature=temperature,
                predicted_voltage=voltage, voltage_error=0.0,
                innovation=0.0, likelihood=0.0, is_valid=False
            )
    
    def _update_statistics(self, result: EstimationResult):
        """更新性能统计"""
        self.total_estimations += 1
        
        if result.is_valid:
            self.successful_estimations += 1
            
            # 更新平均电压误差 (指数移动平均)
            alpha = 0.1
            self.average_voltage_error = (1 - alpha) * self.average_voltage_error + alpha * abs(result.voltage_error)
    
    def get_current_estimates(self) -> Dict[str, float]:
        """获取当前估算值"""
        if not self.estimation_history:
            return {}
        
        latest = self.estimation_history[-1]
        return {
            'soc': latest.soc,
            'soc_uncertainty': latest.soc_uncertainty,
            'r0': latest.r0,
            'r1': latest.r1,
            'c1': latest.c1,
            'v1': latest.v1,
            'voltage_error': latest.voltage_error,
            'likelihood': latest.likelihood
        }
    
    def get_estimation_history(self) -> List[EstimationResult]:
        """获取估算历史"""
        return self.estimation_history.copy()
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """获取性能指标"""
        if self.total_estimations == 0:
            return {}
        
        success_rate = self.successful_estimations / self.total_estimations
        
        # 计算最近的电压误差统计
        recent_errors = [abs(r.voltage_error) for r in self.estimation_history[-100:] if r.is_valid]
        
        return {
            'total_estimations': self.total_estimations,
            'success_rate': success_rate,
            'average_voltage_error': self.average_voltage_error,
            'recent_max_voltage_error': max(recent_errors) if recent_errors else 0.0,
            'recent_mean_voltage_error': np.mean(recent_errors) if recent_errors else 0.0,
            'recent_std_voltage_error': np.std(recent_errors) if recent_errors else 0.0
        }
    
    def reset(self):
        """重置估算器"""
        self.ukf_core.reset()
        self.estimation_history.clear()
        self.data_buffer.clear()
        self.is_initialized = False
        self.total_estimations = 0
        self.successful_estimations = 0
        self.average_voltage_error = 0.0
        
        logging.info("状态估算器已重置")

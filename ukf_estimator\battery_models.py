"""
电池等效电路模型

实现一阶RC等效电路模型，用于UKF状态估算。
包含SOC-OCV关系、内阻模型和温度效应。
"""

import numpy as np
from typing import Dict, Optional, Tuple, List
from dataclasses import dataclass
import logging


@dataclass
class BatteryParameters:
    """电池参数配置"""
    nominal_capacity: float = 280.0      # 标称容量 (Ah)
    nominal_voltage: float = 3.2         # 标称电压 (V)
    r0_initial: float = 0.080           # 初始欧姆内阻 (Ω) - 基于观测函数诊断调整
    r1_initial: float = 0.020           # 初始极化内阻 (Ω) - 基于观测函数诊断调整
    c1_initial: float = 3000.0          # 初始极化电容 (F)
    
    # SOC-OCV查找表 (如果为None，将使用默认模型)
    soc_ocv_table: Optional[Dict[float, float]] = None
    
    # 温度系数
    temp_coeff_r0: float = 0.02         # 欧姆内阻温度系数 (/°C)
    temp_coeff_r1: float = 0.015        # 极化内阻温度系数 (/°C)
    temp_coeff_capacity: float = 0.001  # 容量温度系数 (/°C)
    
    # 参数边界 - 修正范围以匹配PyBaMM
    r0_min: float = 0.005               # 欧姆内阻最小值 (5mΩ)
    r0_max: float = 0.200               # 欧姆内阻最大值 (200mΩ)
    r1_min: float = 0.005               # 极化内阻最小值 (5mΩ)
    r1_max: float = 0.100               # 极化内阻最大值 (100mΩ)
    c1_min: float = 500.0               # 极化电容最小值 (F)
    c1_max: float = 8000.0              # 极化电容最大值 (F)


class FirstOrderRCModel:
    """
    一阶RC等效电路模型
    
    电路结构: OCV -- R0 -- (R1||C1) -- Terminal
    
    状态变量:
    - x[0]: SOC (State of Charge)
    - x[1]: V1 (极化电压)
    - x[2]: R0 (欧姆内阻) - 可选估算
    - x[3]: R1 (极化内阻) - 可选估算  
    - x[4]: C1 (极化电容) - 可选估算
    
    观测变量:
    - y: 端电压 V_terminal
    """
    
    def __init__(self, parameters: BatteryParameters, estimate_parameters: bool = True):
        """
        初始化一阶RC模型
        
        Args:
            parameters: 电池参数
            estimate_parameters: 是否估算R0, R1, C1参数
        """
        self.params = parameters
        self.estimate_parameters = estimate_parameters
        
        # 状态维度
        if estimate_parameters:
            self.n_states = 5  # [SOC, V1, R0, R1, C1]
        else:
            self.n_states = 2  # [SOC, V1]
        
        # 构建SOC-OCV查找表
        self._build_soc_ocv_table()
        
        logging.info(f"一阶RC模型初始化: 状态维度={self.n_states}, 参数估算={estimate_parameters}")
    
    def _build_soc_ocv_table(self):
        """构建SOC-OCV查找表"""
        if self.params.soc_ocv_table is not None:
            self.soc_points = np.array(sorted(self.params.soc_ocv_table.keys()))
            self.ocv_points = np.array([self.params.soc_ocv_table[soc] for soc in self.soc_points])
        else:
            # 使用基于诊断结果修正的LiFePO4 SOC-OCV关系
            # 根据诊断结果：SOC=0.1->3.607V, SOC=0.2->3.674V, SOC=0.6->4.152V, SOC=0.8->4.405V
            self.soc_points = np.array([0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0])
            self.ocv_points = np.array([3.5, 3.607, 3.674, 3.725, 3.815, 3.991, 4.152, 4.279, 4.405, 4.525, 4.6])
        
        logging.debug(f"SOC-OCV表构建完成: {len(self.soc_points)}个数据点")
    
    def get_ocv(self, soc: float) -> float:
        """
        根据SOC获取开路电压
        
        Args:
            soc: 电量状态 (0-1)
            
        Returns:
            开路电压 (V)
        """
        soc = np.clip(soc, 0.0, 1.0)
        return np.interp(soc, self.soc_points, self.ocv_points)
    
    def get_docv_dsoc(self, soc: float) -> float:
        """
        计算dOCV/dSOC (用于UKF线性化)
        
        Args:
            soc: 电量状态 (0-1)
            
        Returns:
            OCV对SOC的导数 (V)
        """
        # 使用数值微分计算导数
        delta_soc = 0.001
        soc_plus = min(soc + delta_soc, 1.0)
        soc_minus = max(soc - delta_soc, 0.0)
        
        ocv_plus = self.get_ocv(soc_plus)
        ocv_minus = self.get_ocv(soc_minus)
        
        return (ocv_plus - ocv_minus) / (soc_plus - soc_minus)
    
    def state_transition(self, x: np.ndarray, dt: float, current: float, temperature: float = 25.0) -> np.ndarray:
        """
        状态转移函数 (非线性)
        
        Args:
            x: 当前状态向量
            dt: 时间步长 (s)
            current: 电流 (A, 正为充电)
            temperature: 温度 (°C)
            
        Returns:
            下一时刻状态向量
        """
        x_next = x.copy()
        
        # 提取当前状态
        soc = x[0]
        v1 = x[1]
        
        if self.estimate_parameters:
            r0 = np.clip(x[2], self.params.r0_min, self.params.r0_max)
            r1 = np.clip(x[3], self.params.r1_min, self.params.r1_max)
            c1 = np.clip(x[4], self.params.c1_min, self.params.c1_max)
        else:
            r0 = self.params.r0_initial
            r1 = self.params.r1_initial
            c1 = self.params.c1_initial
        
        # 温度修正
        temp_factor_r = np.exp(self.params.temp_coeff_r0 * (25.0 - temperature))
        temp_factor_capacity = 1.0 + self.params.temp_coeff_capacity * (temperature - 25.0)
        
        r0 *= temp_factor_r
        r1 *= temp_factor_r
        effective_capacity = self.params.nominal_capacity * temp_factor_capacity
        
        # 1. SOC更新 (库仑计法) - 修正符号约定
        # 注意：PyBaMM约定：正电流为充电，负电流为放电
        # SOC变化：充电时SOC增加，放电时SOC减少
        dsoc = current * dt / (3600.0 * effective_capacity)  # 正电流(充电)增加SOC
        x_next[0] = np.clip(soc + dsoc, 0.001, 0.999)  # 使用更宽松的边界值，允许接近真实范围
        
        # 2. 极化电压更新 (一阶RC动态) - 修正符号约定
        tau = r1 * c1  # 时间常数
        if tau > 1e-6:
            exp_factor = np.exp(-dt / tau)
            # 极化电压方程: V1 = V1*exp(-dt/tau) + R1*I*(1-exp(-dt/tau))
            # 充电时(I>0): V1增加，放电时(I<0): V1减少
            v1_new = v1 * exp_factor + r1 * current * (1 - exp_factor)
            x_next[1] = np.clip(v1_new, -0.5, 0.5)  # 限制在合理范围内
        else:
            x_next[1] = np.clip(r1 * current, -0.5, 0.5)
        
        # 3. 参数更新 (如果启用参数估算)
        if self.estimate_parameters:
            # 参数被建模为随机游走过程 (在UKF中通过过程噪声处理)
            x_next[2] = x[2]  # R0保持不变 + 过程噪声
            x_next[3] = x[3]  # R1保持不变 + 过程噪声
            x_next[4] = x[4]  # C1保持不变 + 过程噪声
        
        return x_next
    
    def observation(self, x: np.ndarray, temperature: float = 25.0) -> float:
        """
        观测函数 (非线性)
        
        Args:
            x: 状态向量
            temperature: 温度 (°C)
            
        Returns:
            端电压 (V)
        """
        # 提取状态
        soc = x[0]
        v1 = x[1]
        
        if self.estimate_parameters:
            r0 = np.clip(x[2], self.params.r0_min, self.params.r0_max)
        else:
            r0 = self.params.r0_initial
        
        # 温度修正
        temp_factor_r = np.exp(self.params.temp_coeff_r0 * (25.0 - temperature))
        r0 *= temp_factor_r
        
        # 获取开路电压
        ocv = self.get_ocv(soc)

        # 计算端电压: V_terminal = OCV - V1 (简化观测方程)
        # 注意: 这里使用简化的观测方程，假设欧姆内阻的影响已经包含在极化电压中
        v_terminal = ocv - v1
        
        return v_terminal
    
    def observation_with_current(self, x: np.ndarray, current: float, temperature: float = 25.0) -> float:
        """
        包含电流的观测函数 (更准确的模型)
        
        Args:
            x: 状态向量
            current: 电流 (A, 正为充电)
            temperature: 温度 (°C)
            
        Returns:
            端电压 (V)
        """
        # 提取状态
        soc = x[0]
        v1 = x[1]
        
        if self.estimate_parameters:
            r0 = np.clip(x[2], self.params.r0_min, self.params.r0_max)
        else:
            r0 = self.params.r0_initial
        
        # 温度修正
        temp_factor_r = np.exp(self.params.temp_coeff_r0 * (25.0 - temperature))
        r0 *= temp_factor_r
        
        # 获取开路电压
        ocv = self.get_ocv(soc)
        
        # 计算端电压: V_terminal = OCV - I*R0 - V1
        # 注意：PyBaMM中的符号约定
        # 充电: I>0, 端电压 = OCV - I*R0 - V1 (考虑内阻压降)
        # 放电: I<0, 端电压 = OCV - I*R0 - V1 (内阻压降为负，电压升高)
        # 修正：r0本身就是正数，不需要abs()
        v_terminal = ocv - current * r0 - v1
        
        return v_terminal
    
    def get_initial_state(self, initial_soc: float, temperature: float = 25.0) -> np.ndarray:
        """
        获取初始状态向量
        
        Args:
            initial_soc: 初始SOC
            temperature: 温度 (°C)
            
        Returns:
            初始状态向量
        """
        if self.estimate_parameters:
            x0 = np.array([
                initial_soc,                    # SOC
                0.0,                           # V1 (初始极化电压为0)
                self.params.r0_initial,        # R0
                self.params.r1_initial,        # R1
                self.params.c1_initial         # C1
            ])
        else:
            x0 = np.array([
                initial_soc,                    # SOC
                0.0                            # V1
            ])
        
        return x0
    
    def get_initial_covariance(self) -> np.ndarray:
        """
        获取初始状态协方差矩阵
        
        Returns:
            初始协方差矩阵
        """
        if self.estimate_parameters:
            # 对角协方差矩阵 - 调整初始不确定性以提高收敛性
            P0 = np.diag([
                0.01**2,                       # SOC不确定性 (1%) - 减小初始不确定性
                0.005**2,                      # V1不确定性 (5mV) - 减小初始不确定性
                (0.010)**2,                    # R0不确定性 (10mΩ) - 增大以允许更大调整
                (0.010)**2,                    # R1不确定性 (10mΩ) - 增大以允许更大调整
                (500.0)**2                     # C1不确定性 (500F) - 增大以允许更大调整
            ])
        else:
            P0 = np.diag([
                0.01**2,                       # SOC不确定性 (1%)
                0.005**2                       # V1不确定性 (5mV)
            ])
        
        return P0
    
    def extract_parameters(self, x: np.ndarray) -> Dict[str, float]:
        """
        从状态向量中提取电池参数
        
        Args:
            x: 状态向量
            
        Returns:
            参数字典
        """
        result = {
            'soc': x[0],
            'v1': x[1]
        }
        
        if self.estimate_parameters and len(x) >= 5:
            result.update({
                'r0': x[2],
                'r1': x[3], 
                'c1': x[4]
            })
        else:
            result.update({
                'r0': self.params.r0_initial,
                'r1': self.params.r1_initial,
                'c1': self.params.c1_initial
            })
        
        return result

    def validate_state(self, x: np.ndarray) -> np.ndarray:
        """
        验证和约束状态向量

        Args:
            x: 状态向量

        Returns:
            约束后的状态向量
        """
        x_constrained = x.copy()

        # SOC约束 - 使用更宽松的边界值，允许接近真实范围
        x_constrained[0] = np.clip(x[0], 0.001, 0.999)

        # 极化电压约束 (合理范围)
        x_constrained[1] = np.clip(x[1], -0.5, 0.5)

        # 参数约束
        if self.estimate_parameters and len(x) >= 5:
            x_constrained[2] = np.clip(x[2], self.params.r0_min, self.params.r0_max)
            x_constrained[3] = np.clip(x[3], self.params.r1_min, self.params.r1_max)
            x_constrained[4] = np.clip(x[4], self.params.c1_min, self.params.c1_max)

        return x_constrained

"""
PyBaMM版本适配层

处理不同PyBaMM版本间的参数名称差异和接口变化，
确保系统能在不同PyBaMM版本上稳定运行。
"""

import logging
import numpy as np
from typing import Dict, Any, Optional, Tuple

try:
    import pybamm
    PYBAMM_AVAILABLE = True
    PYBAMM_VERSION = pybamm.__version__
except ImportError:
    PYBAMM_AVAILABLE = False
    PYBAMM_VERSION = None


class PyBaMMVersionAdapter:
    """PyBaMM版本适配器"""
    
    def __init__(self):
        """初始化版本适配器"""
        self.version = PYBAMM_VERSION
        self.major_version = None
        self.minor_version = None
        
        if PYBAMM_AVAILABLE:
            try:
                version_parts = PYBAMM_VERSION.split('.')
                self.major_version = int(version_parts[0])
                self.minor_version = int(version_parts[1])
            except:
                logging.warning(f"无法解析PyBaMM版本: {PYBAMM_VERSION}")
        
        logging.info(f"PyBaMM版本适配器初始化: {PYBAMM_VERSION}")
    
    def get_parameter_set(self, chemistry: str) -> Any:
        """
        获取适配的参数集 (基于PyBaMM官方文档方法)

        Args:
            chemistry: 电池化学体系

        Returns:
            PyBaMM参数集对象
        """
        if not PYBAMM_AVAILABLE:
            raise RuntimeError("PyBaMM不可用")

        try:
            # 方法1: 使用PyBaMM官方推荐的方式 - 从模型获取默认参数
            logging.info("尝试使用PyBaMM官方推荐方法获取参数...")

            # 创建一个临时模型来获取默认参数
            temp_model = pybamm.lithium_ion.SPM()
            default_params = temp_model.default_parameter_values

            if default_params is not None:
                logging.info("成功获取PyBaMM默认参数集")
                return default_params

            # 方法2: 如果方法1失败，尝试预定义参数集
            logging.info("尝试预定义参数集...")
            if chemistry == "LiFePO4":
                parameter_sets = ["Chen2020", "Prada2013"]
            elif chemistry == "NMC":
                parameter_sets = ["Chen2020", "Marquis2019"]
            else:
                parameter_sets = ["Chen2020"]

            for param_set in parameter_sets:
                try:
                    params = pybamm.ParameterValues(param_set)
                    logging.info(f"成功加载参数集: {param_set}")
                    return params
                except Exception as e:
                    logging.debug(f"参数集 {param_set} 加载失败: {e}")
                    continue

            # 方法3: 最后的备用方案
            logging.warning("使用备用参数集")
            return self._create_fallback_parameters()

        except Exception as e:
            logging.error(f"获取参数集失败: {e}")
            return self._create_fallback_parameters()
    
    def _create_fallback_parameters(self) -> Any:
        """创建备用参数集 - 使用PyBaMM内置默认值"""
        try:
            # 强制使用Chen2020作为最后的备用方案
            logging.warning("使用Chen2020作为备用参数集")
            params = pybamm.ParameterValues("Chen2020")

            # 调整电压截止范围，确保PyBaMM不会在BMS保护之前触发事件
            # BMS过压保护通常在4.1-4.25V，欠压保护在2.4-2.7V
            # PyBaMM的截止范围应该更宽，让BMS先触发保护
            params.update({
                "Lower voltage cut-off [V]": 1.0,   # 低于BMS欠压阈值，让BMS先触发
                "Upper voltage cut-off [V]": 4.8,   # 高于BMS过压阈值，让BMS先触发
            })

            logging.info("备用参数集创建成功")
            return params

        except Exception as e:
            logging.error(f"备用参数集创建失败: {e}")
            # 最后的最后，使用PyBaMM的内置默认值
            try:
                params = pybamm.ParameterValues()
                logging.warning("使用PyBaMM内置默认参数")
                return params
            except Exception as e2:
                logging.error(f"PyBaMM内置默认参数也失败: {e2}")
                raise RuntimeError("无法创建任何参数集")

    def set_robust_initial_conditions(self, params: Any, target_soc: float) -> Any:
        """
        设置鲁棒的初始条件 - 使用PyBaMM官方方法

        Args:
            params: PyBaMM参数对象
            target_soc: 目标SOC (0-1)

        Returns:
            更新后的参数对象
        """
        try:
            import pybamm

            # 方法1: 尝试使用PyBaMM的官方初始SOC设置
            try:
                # 使用PyBaMM的内置方法设置初始SOC
                # 这个方法会自动计算正确的浓度
                params.update({"Initial SOC": target_soc})

                logging.debug(f"使用PyBaMM官方方法设置SOC: {target_soc:.3f}")
                return params

            except Exception as e:
                logging.debug(f"PyBaMM官方SOC设置失败: {e}")

            # 方法2: 使用化学计量数设置 (更准确的方法)
            try:
                # 获取化学计量数范围参数
                stoich_n_min = params.get("Minimum stoichiometry in negative electrode", 0.01)
                stoich_n_max = params.get("Maximum stoichiometry in negative electrode", 0.99)
                stoich_p_min = params.get("Minimum stoichiometry in positive electrode", 0.01)
                stoich_p_max = params.get("Maximum stoichiometry in positive electrode", 0.99)

                # 基于化学计量数计算初始值
                # 负极：SOC越高，化学计量数越高
                stoich_n_init = stoich_n_min + target_soc * (stoich_n_max - stoich_n_min)
                # 正极：SOC越高，化学计量数越低（锂离子转移出去）
                stoich_p_init = stoich_p_max - target_soc * (stoich_p_max - stoich_p_min)

                # 获取最大浓度
                c_n_max = params["Maximum concentration in negative electrode [mol.m-3]"]
                c_p_max = params["Maximum concentration in positive electrode [mol.m-3]"]

                # 根据化学计量数计算浓度
                c_n_init = c_n_max * stoich_n_init
                c_p_init = c_p_max * stoich_p_init

                # 更新参数
                params.update({
                    "Initial concentration in negative electrode [mol.m-3]": c_n_init,
                    "Initial concentration in positive electrode [mol.m-3]": c_p_init,
                })

                logging.debug(f"使用化学计量数设置SOC: 目标={target_soc:.3f}, "
                             f"负极化学计量数={stoich_n_init:.3f}, 正极化学计量数={stoich_p_init:.3f}")
                return params

            except Exception as e:
                logging.debug(f"化学计量数方法失败: {e}")

            # 方法3: 备用的简化方法
            c_n_max = params["Maximum concentration in negative electrode [mol.m-3]"]
            c_p_max = params["Maximum concentration in positive electrode [mol.m-3]"]

            # 使用经验公式 (基于典型锂离子电池)
            # 这个公式是基于实际电池测试数据调整的
            safety_margin = 0.05

            # 负极浓度：SOC越高，浓度越高（但不是线性关系）
            c_n_ratio = 0.1 + target_soc * 0.8
            # 正极浓度：SOC越高，浓度越低
            c_p_ratio = 0.9 - target_soc * 0.8

            # 应用安全边际
            c_n_ratio = max(0.1, min(0.9, c_n_ratio))
            c_p_ratio = max(0.1, min(0.9, c_p_ratio))

            c_n_init = c_n_max * c_n_ratio
            c_p_init = c_p_max * c_p_ratio

            params.update({
                "Initial concentration in negative electrode [mol.m-3]": c_n_init,
                "Initial concentration in positive electrode [mol.m-3]": c_p_init,
            })

            logging.debug(f"使用备用方法设置SOC: 目标={target_soc:.3f}, "
                         f"负极浓度比={c_n_ratio:.3f}, 正极浓度比={c_p_ratio:.3f}")

            return params

        except Exception as e:
            logging.error(f"设置鲁棒初始条件失败: {e}")
            return params

    def _create_default_parameters(self, chemistry: str) -> Any:
        """创建默认参数集"""
        try:
            # 创建空的参数值对象
            params = pybamm.ParameterValues({})
            
            # 根据化学体系设置基本参数
            if chemistry == "LiFePO4":
                default_params = self._get_lifepo4_default_params()
            elif chemistry == "NMC":
                default_params = self._get_nmc_default_params()
            else:
                default_params = self._get_lifepo4_default_params()
            
            # 更新参数
            params.update(default_params)
            
            logging.info(f"创建默认{chemistry}参数集成功")
            return params
            
        except Exception as e:
            logging.error(f"创建默认参数集失败: {e}")
            raise
    
    def _get_lifepo4_default_params(self) -> Dict[str, float]:
        """获取LiFePO4默认参数 (使用PyBaMM 25.x版本的正确参数名)"""
        return {
            # 基本电池参数
            "Nominal cell capacity [A.h]": 2.3,  # 使用PyBaMM默认值
            # 删除 "Current function [A]": 实验框架会自动处理

            # 几何参数 (使用正确的参数名)
            "Electrode height [m]": 0.065,
            "Electrode width [m]": 0.1016,
            "Cell thickness [m]": 0.0271,

            # 负极参数 (使用正确的参数名)
            "Negative electrode thickness [m]": 85.2e-6,
            "Negative particle radius [m]": 5.86e-6,
            "Negative electrode porosity": 0.485,
            "Negative electrode active material volume fraction": 0.665,

            # 正极参数 (使用正确的参数名)
            "Positive electrode thickness [m]": 75.6e-6,
            "Positive particle radius [m]": 5.22e-6,
            "Positive electrode porosity": 0.385,
            "Positive electrode active material volume fraction": 0.665,

            # 隔膜参数
            "Separator thickness [m]": 12e-6,
            "Separator porosity": 0.724,

            # 电导率参数 (使用正确的参数名)
            "Negative electrode conductivity [S.m-1]": 215.0,
            "Positive electrode conductivity [S.m-1]": 0.18,

            # 扩散系数 (使用正确的参数名)
            "Negative electrode diffusivity [m2.s-1]": 3.3e-14,
            "Positive electrode diffusivity [m2.s-1]": 4e-15,

            # 热参数
            "Cell thermal mass [J.K-1]": 1000.0,
            "Cell-jig heat transfer coefficient [W.m-2.K-1]": 10.0,
            "Jig thermal mass [J.K-1]": 5000.0,
            "Jig-air heat transfer coefficient [W.m-2.K-1]": 10.0,

            # 初始条件
            "Initial concentration in negative electrode [mol.m-3]": 29866.0,
            "Initial concentration in positive electrode [mol.m-3]": 17038.0,
            "Initial temperature [K]": 298.15,

            # 反应动力学 (使用正确的参数名)
            "Negative electrode exchange-current density [A.m-2]": 4.824,
            "Positive electrode exchange-current density [A.m-2]": 2.334,
        }
    
    def _get_nmc_default_params(self) -> Dict[str, float]:
        """获取NMC默认参数"""
        params = self._get_lifepo4_default_params()
        
        # 修改NMC特有参数
        params.update({
            "Positive electrode OCP [V]": 4.0,
            "Negative electrode OCP [V]": 0.1,
        })
        
        return params
    
    def create_model(self, model_type: str = "SPM", include_thermal: bool = True) -> Any:
        """
        创建适配的PyBaMM模型
        
        Args:
            model_type: 模型类型 (SPM, SPMe, DFN)
            include_thermal: 是否包含热效应
            
        Returns:
            PyBaMM模型对象
        """
        if not PYBAMM_AVAILABLE:
            raise RuntimeError("PyBaMM不可用")
        
        try:
            # 设置模型选项
            options = {}
            if include_thermal:
                options["thermal"] = "lumped"
            
            # 创建模型
            if model_type == "SPM":
                model = pybamm.lithium_ion.SPM(options)
            elif model_type == "SPMe":
                model = pybamm.lithium_ion.SPMe(options)
            elif model_type == "DFN":
                model = pybamm.lithium_ion.DFN(options)
            else:
                logging.warning(f"未知模型类型 {model_type}，使用SPM")
                model = pybamm.lithium_ion.SPM(options)
            
            logging.info(f"成功创建{model_type}模型 (热效应: {include_thermal})")
            return model
            
        except Exception as e:
            logging.error(f"创建模型失败: {e}")
            raise
    
    def create_simulation(self, model: Any, parameter_values: Any) -> Any:
        """
        创建适配的仿真对象 (使用鲁棒的IDAKLUSolver)

        Args:
            model: PyBaMM模型
            parameter_values: 参数值

        Returns:
            PyBaMM仿真对象
        """
        if not PYBAMM_AVAILABLE:
            raise RuntimeError("PyBaMM不可用")

        try:
            # 基于深入分析：使用鲁棒的IDAKLUSolver配置
            try:
                # 宽松的容差设置，提高低SOC稳定性
                solver = pybamm.IDAKLUSolver(atol=1e-8, rtol=1e-6)
                logging.info("使用鲁棒IDAKLUSolver求解器")
            except:
                # 如果IDAKLUSolver不可用，使用宽松的CasadiSolver
                solver = pybamm.CasadiSolver(atol=1e-6, rtol=1e-4)
                logging.info("回退到鲁棒CasadiSolver求解器")

            # 创建仿真对象
            simulation = pybamm.Simulation(
                model=model,
                parameter_values=parameter_values,
                solver=solver
            )

            logging.info("PyBaMM鲁棒仿真对象创建成功")
            return simulation

        except Exception as e:
            logging.error(f"创建仿真对象失败: {e}")
            raise

    def set_initial_conditions(self, simulation: Any, initial_soc: float = 0.5,
                              initial_temperature: float = 25.0) -> bool:
        """
        设置仿真的初始条件

        Args:
            simulation: PyBaMM仿真对象
            initial_soc: 初始SOC (0-1)
            initial_temperature: 初始温度 (°C)

        Returns:
            bool: 设置是否成功
        """
        try:
            # 方法1: 通过参数值设置初始条件
            if hasattr(simulation, 'parameter_values'):
                # 设置初始SOC相关参数
                simulation.parameter_values.update({
                    "Initial concentration in negative electrode [mol.m-3]":
                        29866.0 * initial_soc,  # 基于Chen2020参数
                    "Initial concentration in positive electrode [mol.m-3]":
                        17038.0 * (1.0 - initial_soc),  # 基于Chen2020参数
                    "Initial temperature [K]": initial_temperature + 273.15
                })

                logging.debug(f"通过参数值设置初始条件: SOC={initial_soc:.3f}, T={initial_temperature:.1f}°C")
                return True

            # 方法2: 如果有其他设置方法，可以在这里添加
            else:
                logging.warning("仿真对象没有parameter_values属性，无法设置初始条件")
                return False

        except Exception as e:
            logging.error(f"设置初始条件失败: {e}")
            return False

    def solve_step(self, simulation: Any, current: float, dt: float,
                   initial_soc: Optional[float] = None) -> Tuple[bool, Any]:
        """
        执行一个仿真步骤 (使用PyBaMM实验框架 - 推荐方法)

        Args:
            simulation: PyBaMM仿真对象 (仅用于获取模型和参数)
            current: 电流 (A, 正为充电，负为放电)
            dt: 时间步长 (s)
            initial_soc: 初始SOC (可选，暂未使用)

        Returns:
            (成功标志, 解对象)
        """
        if not PYBAMM_AVAILABLE:
            return False, None

        try:
            # 使用PyBaMM实验框架 - 这是推荐的正确方法
            return self._solve_with_experiment(simulation, current, dt)

        except Exception as e:
            logging.error(f"PyBaMM实验框架求解异常: {e}")
            return False, None

    def _solve_with_experiment(self, simulation: Any, current: float, dt: float) -> Tuple[bool, Any]:
        """使用PyBaMM实验框架求解 - 修复状态累积问题"""
        try:
            import pybamm

            # 检查是否为容量异常模式（禁用累积实验）
            capacity_anomaly_mode = getattr(simulation, '_capacity_anomaly_mode', False)

            if capacity_anomaly_mode:
                # 容量异常模式：使用单步实验，不累积历史
                if abs(current) < 1e-6:
                    experiment_step = f"Rest for {dt} seconds"
                elif current > 0:
                    experiment_step = f"Charge at {current}A for {dt} seconds"
                else:
                    experiment_step = f"Discharge at {abs(current)}A for {dt} seconds"

                logging.debug(f"PyBaMM单步实验 (容量异常模式): {experiment_step}")

                # 创建单步实验
                single_experiment = pybamm.Experiment([experiment_step])

                # 创建新的仿真对象（每次都是全新的）
                sim_with_experiment = pybamm.Simulation(
                    simulation.model,
                    parameter_values=simulation.parameter_values,
                    experiment=single_experiment,
                    solver=simulation.solver
                )

            else:
                # 正常模式：使用累积实验
                # 初始化累积实验历史
                if not hasattr(simulation, '_experiment_history'):
                    simulation._experiment_history = []
                    simulation._total_time = 0.0

                # 创建当前步骤
                if abs(current) < 1e-6:
                    experiment_step = f"Rest for {dt} seconds"
                elif current > 0:
                    experiment_step = f"Charge at {current}A for {dt} seconds"
                else:
                    experiment_step = f"Discharge at {abs(current)}A for {dt} seconds"

                # 添加到历史
                simulation._experiment_history.append(experiment_step)
                simulation._total_time += dt

                logging.debug(f"PyBaMM累积实验步骤: {experiment_step} (总时间: {simulation._total_time}s)")

                # 创建包含所有历史步骤的完整实验
                full_experiment = pybamm.Experiment(simulation._experiment_history)

                # 创建仿真对象（累积模式）
                sim_with_experiment = pybamm.Simulation(
                    simulation.model,
                    parameter_values=simulation.parameter_values,
                    experiment=full_experiment,
                    solver=simulation.solver
                )

            # 求解实验
            solution = sim_with_experiment.solve()

            # 检查求解是否成功
            if self._is_solution_successful(solution):
                if capacity_anomaly_mode:
                    logging.debug(f"PyBaMM单步实验求解成功 (容量异常模式)")
                else:
                    logging.debug(f"PyBaMM累积实验求解成功，总步数: {len(simulation._experiment_history)}")
                return True, solution
            else:
                if capacity_anomaly_mode:
                    logging.warning(f"PyBaMM单步实验求解失败 (容量异常模式)")
                else:
                    logging.warning(f"PyBaMM累积实验求解失败")
                return False, None

        except Exception as e:
            logging.error(f"PyBaMM累积实验异常: {e}")
            return False, None

    def _is_solution_successful(self, solution: Any) -> bool:
        """检查PyBaMM 25.6.0解是否成功 (正确方法)"""
        if solution is None:
            return False

        try:
            # 检查是否有电压数据
            voltage_data = solution["Terminal voltage [V]"].entries
            if len(voltage_data) == 0:
                return False

            # 检查电压是否合理 (2-5V范围)
            final_voltage = voltage_data[-1]
            if not (2.0 <= final_voltage <= 5.0):
                return False

            # 检查是否有时间数据
            time_data = solution["Time [s]"].entries
            if len(time_data) == 0:
                return False

            return True

        except Exception:
            return False


    
    def extract_results(self, solution: Any) -> Dict[str, float]:
        """
        从PyBaMM解中提取结果 (使用PyBaMM 25.6.0正确的变量名)

        Args:
            solution: PyBaMM解对象

        Returns:
            结果字典
        """
        if not PYBAMM_AVAILABLE or solution is None:
            return {}

        try:
            results = {}

            # 1. 提取电压 (确认可用)
            try:
                voltage_data = solution["Terminal voltage [V]"].entries
                results['voltage'] = float(voltage_data[-1])
            except Exception as e:
                logging.debug(f"提取电压失败: {e}")

            # 2. 提取电流 (确认可用)
            try:
                current_data = solution["Current [A]"].entries
                results['current'] = float(current_data[-1])
            except Exception as e:
                logging.debug(f"提取电流失败: {e}")

            # 3. 提取温度 (使用正确的变量名)
            try:
                temp_data = solution["X-averaged cell temperature [K]"].entries
                results['temperature'] = float(temp_data[-1]) - 273.15  # 转换为摄氏度
            except Exception as e:
                logging.debug(f"提取温度失败: {e}")

            # 4. 计算SOC (使用化学计量数) - 修复SOC计算公式
            try:
                # 使用负极平均化学计量数计算SOC
                neg_stoich_data = solution["Average negative particle stoichiometry"].entries
                neg_stoich = float(neg_stoich_data[-1])

                # 修复SOC计算公式
                # 对于锂离子电池，负极化学计量数越高，SOC越高
                # 需要根据负极材料的化学计量数范围进行映射
                # 假设负极化学计量数范围为0.01-0.99 (典型石墨负极)
                stoich_min = 0.01  # 完全放电时的化学计量数
                stoich_max = 0.99  # 完全充电时的化学计量数
                calculated_soc = (neg_stoich - stoich_min) / (stoich_max - stoich_min)
                results['soc'] = max(0.0, min(1.0, calculated_soc))  # 限制在0-1范围

            except Exception as e:
                logging.debug(f"SOC计算失败: {e}")
                # 备用SOC计算方法
                try:
                    pos_stoich_data = solution["Average positive particle stoichiometry"].entries
                    pos_stoich = float(pos_stoich_data[-1])
                    results['soc'] = max(0.0, min(1.0, pos_stoich))
                except:
                    results['soc'] = 0.5  # 默认值

            # 5. 提取其他有用变量
            try:
                # 尝试获取开路电压相关信息
                if "Battery open-circuit voltage [V]" in solution.all_models[0].variables_and_events:
                    ocv_data = solution["Battery open-circuit voltage [V]"].entries
                    results['ocv'] = float(ocv_data[-1])
                else:
                    # 如果没有OCV，使用电压作为近似
                    results['ocv'] = results.get('voltage', 0.0)
            except:
                results['ocv'] = results.get('voltage', 0.0)

            # 6. 提取时间信息
            try:
                time_data = solution["Time [s]"].entries
                results['time'] = float(time_data[-1])
            except:
                pass

            logging.debug(f"成功提取PyBaMM结果: {list(results.keys())}")
            return results

        except Exception as e:
            logging.error(f"提取PyBaMM结果失败: {e}")
            return {}
    
    def search_parameters(self, params: Any, search_term: str) -> Dict[str, Any]:
        """
        搜索参数集中的参数 (使用PyBaMM官方搜索功能)

        Args:
            params: PyBaMM参数对象
            search_term: 搜索关键词

        Returns:
            匹配的参数字典
        """
        if not PYBAMM_AVAILABLE or params is None:
            return {}

        try:
            # 使用PyBaMM官方的参数搜索功能
            search_results = params.search(search_term)

            if search_results:
                logging.info(f"找到 {len(search_results)} 个包含'{search_term}'的参数")
                return {param: params[param] for param in search_results}
            else:
                logging.warning(f"未找到包含'{search_term}'的参数")
                return {}

        except Exception as e:
            logging.error(f"参数搜索失败: {e}")
            return {}

    def print_parameter_info(self, params: Any, search_term: str = None) -> None:
        """
        打印参数信息 (使用PyBaMM官方功能)

        Args:
            params: PyBaMM参数对象
            search_term: 可选的搜索关键词
        """
        if not PYBAMM_AVAILABLE or params is None:
            return

        try:
            if search_term:
                # 搜索特定参数
                results = self.search_parameters(params, search_term)
                print(f"\n包含'{search_term}'的参数:")
                for param_name, value in results.items():
                    print(f"  {param_name}: {value}")
            else:
                # 使用PyBaMM的内置打印功能
                params.print_parameters()

        except Exception as e:
            logging.error(f"打印参数信息失败: {e}")

    def get_version_info(self) -> Dict[str, Any]:
        """获取版本信息"""
        # 解析patch版本
        patch_version = None
        if PYBAMM_VERSION and '.' in PYBAMM_VERSION:
            version_parts = PYBAMM_VERSION.split('.')
            if len(version_parts) >= 3:
                try:
                    patch_version = int(version_parts[2])
                except ValueError:
                    patch_version = 0

        return {
            'pybamm_available': PYBAMM_AVAILABLE,
            'pybamm_version': PYBAMM_VERSION,
            'major_version': self.major_version,
            'minor_version': self.minor_version,
            'patch_version': patch_version,
            'adapter_ready': PYBAMM_AVAILABLE and self.major_version is not None
        }

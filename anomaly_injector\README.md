# 异常注入系统 (Anomaly Injection System)

基于PyBaMM的物理异常注入仿真框架，用于电池异常诊断算法的开发和测试。

## 🚀 概述

本系统通过在PyBaMM仿真过程中修改物理参数来实现真实的异常注入，为诊断算法提供可控、可重复的测试环境。

### 核心特性

- **物理真实性**: 通过修改PyBaMM物理参数实现异常，而非数据后处理
- **多种异常类型**: 支持内阻、容量、热异常三大类
- **严重程度梯度**: 每种异常支持MILD/MODERATE/SEVERE三个级别
- **时间演化**: 支持异常的动态注入和时间演化特征
- **完整验证**: 提供可视化验证和数值对比

## 📦 模块结构

```
anomaly_injector/
├── __init__.py                 # 模块初始化和导出
├── anomaly_types.py           # 异常类型定义
├── anomaly_injector.py        # 异常注入器核心
├── enhanced_simulators.py     # 增强仿真器
└── README.md                  # 本文档
```

## 🔧 支持的异常类型

### 1. 内阻异常 (ResistanceAnomaly)

**物理机制**: 降低电极交换电流密度和扩散系数，增加内阻

**修改参数**:
- `Negative electrode exchange-current density [A.m-2]`
- `Positive electrode exchange-current density [A.m-2]`
- `Negative particle diffusivity [m2.s-1]`

**效果特征**:
- **MILD**: +408mV 电压上升
- **MODERATE**: +430mV 电压上升
- **SEVERE**: +470mV 电压上升
- **时间特征**: 立即生效，持续影响

### 2. 容量异常 (CapacityAnomaly)

**物理机制**: 减少电极厚度和活性物质体积分数，模拟容量衰减

**修改参数**:
- `Negative electrode thickness [m]`
- `Positive electrode thickness [m]`
- `Negative electrode active material volume fraction`

**效果特征**:
- **MILD**: +3mV 电压变化
- **MODERATE**: +23mV 电压变化
- **SEVERE**: +63mV 电压变化
- **时间特征**: 复杂的先降后升模式，体现容量衰减的双重效应

### 3. 热异常 (ThermalAnomaly)

**物理机制**: 降低热传导能力，提高环境温度，增加产热

**修改参数**:
- `Negative electrode thermal conductivity [W.m-1.K-1]`
- `Positive electrode thermal conductivity [W.m-1.K-1]`
- `Separator thermal conductivity [W.m-1.K-1]`
- `Total heat transfer coefficient [W.m-2.K-1]`
- `Ambient temperature [K]`
- `Negative electrode exchange-current density [A.m-2]`

**效果特征**:
- **MILD**: ****°C 温度上升
- **MODERATE**: ****°C 温度上升
- **SEVERE**: +13.4°C 温度上升
- **时间特征**: 温度随时间累积上升

## 🛠 使用方法

### 基本用法

```python
from anomaly_injector import EnhancedCellSimulator, ResistanceAnomaly, AnomalySeverity

# 创建增强电芯仿真器
cell = EnhancedCellSimulator(cell_id=1, chemistry="LiFePO4", include_thermal=True)
cell.set_initial_conditions(initial_soc=0.5, initial_temperature=25.0)

# 创建内阻异常
anomaly = ResistanceAnomaly(severity=AnomalySeverity.MODERATE)

# 注入异常
success = cell.inject_anomaly("resistance_test", anomaly, 0.0)

# 运行仿真
state = cell.simulate_step(2.0, 25.0, 30.0)
print(f"电压: {state.voltage:.3f}V, 温度: {state.temperature:.2f}°C")
```

### 容量异常示例

```python
from anomaly_injector import EnhancedCellSimulator, CapacityAnomaly, AnomalySeverity

# 创建电芯
cell = EnhancedCellSimulator(cell_id=1, chemistry="LiFePO4", include_thermal=True)
cell.set_initial_conditions(initial_soc=0.5, initial_temperature=25.0)

# 运行几步基线（容量异常建议在有基线历史后注入）
for step in range(3):
    cell.simulate_step(2.0, 25.0, 30.0)

# 注入容量异常
anomaly = CapacityAnomaly(severity=AnomalySeverity.MILD)
success = cell.inject_anomaly("capacity_test", anomaly, 90.0)

# 继续仿真观察效果
state = cell.simulate_step(2.0, 25.0, 30.0)
```

### 热异常示例

```python
from anomaly_injector import EnhancedCellSimulator, ThermalAnomaly, AnomalySeverity

# 创建电芯
cell = EnhancedCellSimulator(cell_id=1, chemistry="LiFePO4", include_thermal=True)
cell.set_initial_conditions(initial_soc=0.5, initial_temperature=25.0)

# 注入热异常
anomaly = ThermalAnomaly(severity=AnomalySeverity.SEVERE)
success = cell.inject_anomaly("thermal_test", anomaly, 0.0)

# 使用较高电流和较长时间以观察热效应
state = cell.simulate_step(3.0, 25.0, 60.0)
print(f"温度: {state.temperature:.2f}°C")
```

## 📊 验证和测试

### 运行完整验证

项目根目录提供了完整的验证脚本：

```bash
# 激活conda环境
source /Users/<USER>/anaconda3/bin/activate bat

# 运行完整验证
python complete_anomaly_verification.py
```

### 验证结果

验证脚本会生成：
- **控制台输出**: 详细的数值对比和测试结果
- **可视化图表**: `complete_anomaly_verification.png` - 所有异常类型的效果对比图

## 🔬 物理原理

### 内阻异常的物理机制

通过降低电极反应动力学参数：
- **交换电流密度降低** → 电荷转移阻抗增加
- **扩散系数降低** → 扩散阻抗增加
- **综合效果** → 内阻增加 → 充电电压升高

### 容量异常的复杂效应

容量异常展现了两种相互竞争的物理效应：

1. **内阻降低效应** (电压上升)
   - 电极厚度减少 → 离子传输路径缩短 → 内阻降低

2. **容量减少效应** (电压下降)
   - 活性物质减少 → 总容量降低 → 电压特性变化

**不同严重程度的表现**:
- **MILD/MODERATE**: 先下降后上升的"V"型变化
- **SEVERE**: 内阻效应占主导，持续上升

### 热异常的累积效应

通过多重热管理参数修改：
- **热传导系数降低** → 散热能力下降
- **环境温度提高** → 基准温度升高
- **内阻增加** → 产热增加
- **综合效果** → 温度随时间累积上升

## ⚙️ 技术实现

### 核心类层次结构

```
AnomalyType (基类)
├── ResistanceAnomaly    # 内阻异常
├── CapacityAnomaly      # 容量异常
├── ThermalAnomaly       # 热异常
└── ShortCircuitAnomaly  # 短路异常 (预留)

EnhancedCellSimulator    # 增强电芯仿真器
├── 继承自 CellSimulator
├── 添加异常注入能力
└── 支持参数动态修改

AnomalyInjector         # 异常注入器 (预留)
└── 批量异常管理
```

### 参数修改策略

1. **内阻异常**: 使用相对乘数，保持参数间比例关系
2. **容量异常**: 使用绝对值，精确控制几何参数
3. **热异常**: 混合策略，热参数用绝对值，电化学参数用相对乘数

## 🎯 诊断算法应用价值

### 异常检测

- **内阻异常**: 电压持续上升模式
- **容量异常**: 复杂的电压变化模式
- **热异常**: 温度异常上升

### 异常分类

- **电压特征**: 上升 vs 下降 vs 复杂变化
- **时间特征**: 立即 vs 渐变 vs 累积
- **物理量**: 电压 vs 温度 vs 多参数

### 严重程度评估

- **内阻异常**: 408mV → 430mV → 470mV 的清晰梯度
- **容量异常**: 3mV → 23mV → 63mV 的梯度
- **热异常**: 4.0°C → 7.7°C → 13.4°C 的梯度

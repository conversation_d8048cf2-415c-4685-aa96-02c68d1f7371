"""
估算验证模块

提供UKF估算结果的验证和性能评估功能。
支持与虚拟电池系统真实值的对比分析。
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from dataclasses import dataclass

from .state_estimator import EstimationResult
from .data_interface import MeasurementData


@dataclass
class ValidationMetrics:
    """验证指标"""
    # SOC估算指标
    soc_mae: float              # SOC平均绝对误差
    soc_rmse: float             # SOC均方根误差
    soc_max_error: float        # SOC最大误差
    soc_correlation: float      # SOC相关系数
    
    # 内阻估算指标
    r0_mae: float               # R0平均绝对误差
    r0_rmse: float              # R0均方根误差
    r0_max_error: float         # R0最大误差
    r0_correlation: float       # R0相关系数
    
    # 电压预测指标
    voltage_mae: float          # 电压平均绝对误差
    voltage_rmse: float         # 电压均方根误差
    voltage_max_error: float    # 电压最大误差
    
    # 整体性能指标
    estimation_success_rate: float    # 估算成功率
    convergence_time: float           # 收敛时间
    computational_efficiency: float   # 计算效率
    
    # 数据点数
    total_points: int
    valid_points: int


class EstimationValidator:
    """
    估算验证器
    
    提供UKF估算结果的全面验证和性能评估。
    支持与虚拟电池系统真实值的对比，生成详细的性能报告。
    """
    
    def __init__(self):
        """初始化验证器"""
        self.validation_history = []
        logging.info("估算验证器初始化完成")
    
    def validate_estimation_results(self, estimation_results: List[EstimationResult],
                                   ground_truth_data: List[MeasurementData]) -> ValidationMetrics:
        """
        验证估算结果
        
        Args:
            estimation_results: UKF估算结果列表
            ground_truth_data: 真实值数据列表
            
        Returns:
            验证指标
        """
        if not estimation_results or not ground_truth_data:
            raise ValueError("估算结果或真实数据为空")
        
        # 对齐数据 (基于时间戳)
        aligned_data = self._align_data(estimation_results, ground_truth_data)
        
        if len(aligned_data) < 10:
            raise ValueError("对齐后的数据点不足，无法进行有效验证")
        
        logging.info(f"开始验证估算结果，对齐数据点数: {len(aligned_data)}")
        
        # 提取数据
        soc_est = np.array([d['soc_est'] for d in aligned_data])
        soc_true = np.array([d['soc_true'] for d in aligned_data])
        r0_est = np.array([d['r0_est'] for d in aligned_data])
        r0_true = np.array([d['r0_true'] for d in aligned_data])
        voltage_pred = np.array([d['voltage_pred'] for d in aligned_data])
        voltage_meas = np.array([d['voltage_meas'] for d in aligned_data])
        is_valid = np.array([d['is_valid'] for d in aligned_data])
        
        # 过滤有效数据
        valid_mask = is_valid & np.isfinite(soc_est) & np.isfinite(soc_true) & \
                    np.isfinite(r0_est) & np.isfinite(r0_true)
        
        if np.sum(valid_mask) < 5:
            raise ValueError("有效数据点不足")
        
        # 计算SOC指标
        soc_metrics = self._calculate_error_metrics(soc_est[valid_mask], soc_true[valid_mask])
        
        # 计算R0指标
        r0_metrics = self._calculate_error_metrics(r0_est[valid_mask], r0_true[valid_mask])
        
        # 计算电压指标
        voltage_metrics = self._calculate_error_metrics(voltage_pred[valid_mask], voltage_meas[valid_mask])
        
        # 计算整体性能指标
        success_rate = np.sum(valid_mask) / len(aligned_data)
        convergence_time = self._calculate_convergence_time(aligned_data)
        
        # 创建验证指标
        metrics = ValidationMetrics(
            # SOC指标
            soc_mae=soc_metrics['mae'],
            soc_rmse=soc_metrics['rmse'],
            soc_max_error=soc_metrics['max_error'],
            soc_correlation=soc_metrics['correlation'],
            
            # R0指标
            r0_mae=r0_metrics['mae'],
            r0_rmse=r0_metrics['rmse'],
            r0_max_error=r0_metrics['max_error'],
            r0_correlation=r0_metrics['correlation'],
            
            # 电压指标
            voltage_mae=voltage_metrics['mae'],
            voltage_rmse=voltage_metrics['rmse'],
            voltage_max_error=voltage_metrics['max_error'],
            
            # 整体指标
            estimation_success_rate=success_rate,
            convergence_time=convergence_time,
            computational_efficiency=1.0,  # 简化计算
            
            # 数据统计
            total_points=len(aligned_data),
            valid_points=int(np.sum(valid_mask))
        )
        
        # 保存验证历史
        self.validation_history.append({
            'timestamp': aligned_data[-1]['timestamp'] if aligned_data else 0.0,
            'metrics': metrics,
            'data_points': len(aligned_data)
        })
        
        logging.info(f"验证完成 - SOC RMSE: {metrics.soc_rmse:.4f}, "
                    f"R0 RMSE: {metrics.r0_rmse:.6f}Ω, 成功率: {metrics.estimation_success_rate:.2%}")
        
        return metrics
    
    def _align_data(self, estimation_results: List[EstimationResult],
                   ground_truth_data: List[MeasurementData]) -> List[Dict]:
        """对齐估算结果和真实数据"""
        aligned_data = []
        
        # 创建真实数据的时间戳索引
        truth_dict = {d.timestamp: d for d in ground_truth_data}
        truth_timestamps = sorted(truth_dict.keys())
        
        for est_result in estimation_results:
            if not est_result.is_valid:
                continue
            
            # 找到最接近的真实数据时间戳
            closest_timestamp = min(truth_timestamps, 
                                  key=lambda t: abs(t - est_result.timestamp))
            
            # 时间差阈值 (例如1秒)
            if abs(closest_timestamp - est_result.timestamp) > 1.0:
                continue
            
            truth_data = truth_dict[closest_timestamp]
            
            # 检查真实数据的有效性
            if (truth_data.soc_true is None or truth_data.r0_true is None or
                not np.isfinite([truth_data.soc_true, truth_data.r0_true]).all()):
                continue
            
            aligned_data.append({
                'timestamp': est_result.timestamp,
                'soc_est': est_result.soc,
                'soc_true': truth_data.soc_true,
                'r0_est': est_result.r0,
                'r0_true': truth_data.r0_true,
                'r1_est': est_result.r1,
                'voltage_pred': est_result.predicted_voltage,
                'voltage_meas': est_result.measured_voltage,
                'is_valid': est_result.is_valid and truth_data.is_valid
            })
        
        return aligned_data
    
    def _calculate_error_metrics(self, estimated: np.ndarray, 
                               true_values: np.ndarray) -> Dict[str, float]:
        """计算误差指标"""
        if len(estimated) != len(true_values) or len(estimated) == 0:
            return {'mae': np.inf, 'rmse': np.inf, 'max_error': np.inf, 'correlation': 0.0}
        
        # 过滤无效值
        valid_mask = np.isfinite(estimated) & np.isfinite(true_values)
        if np.sum(valid_mask) < 2:
            return {'mae': np.inf, 'rmse': np.inf, 'max_error': np.inf, 'correlation': 0.0}
        
        est_valid = estimated[valid_mask]
        true_valid = true_values[valid_mask]
        
        # 计算误差
        errors = est_valid - true_valid
        
        # 计算指标
        mae = np.mean(np.abs(errors))
        rmse = np.sqrt(np.mean(errors**2))
        max_error = np.max(np.abs(errors))
        
        # 计算相关系数
        try:
            correlation = np.corrcoef(est_valid, true_valid)[0, 1]
            if not np.isfinite(correlation):
                correlation = 0.0
        except:
            correlation = 0.0
        
        return {
            'mae': mae,
            'rmse': rmse,
            'max_error': max_error,
            'correlation': correlation
        }
    
    def _calculate_convergence_time(self, aligned_data: List[Dict]) -> float:
        """计算收敛时间"""
        if len(aligned_data) < 10:
            return np.inf
        
        try:
            # 计算SOC误差的移动平均
            soc_errors = [abs(d['soc_est'] - d['soc_true']) for d in aligned_data]
            window_size = min(10, len(soc_errors) // 2)
            
            # 计算移动平均
            moving_avg = []
            for i in range(window_size, len(soc_errors)):
                avg_error = np.mean(soc_errors[i-window_size:i])
                moving_avg.append(avg_error)
            
            if not moving_avg:
                return np.inf
            
            # 找到误差稳定的点 (变化率小于阈值)
            threshold = 0.001  # SOC误差变化阈值
            for i in range(1, len(moving_avg)):
                if abs(moving_avg[i] - moving_avg[i-1]) < threshold:
                    # 找到收敛点，计算时间
                    convergence_index = i + window_size
                    if convergence_index < len(aligned_data):
                        start_time = aligned_data[0]['timestamp']
                        convergence_time = aligned_data[convergence_index]['timestamp']
                        return convergence_time - start_time
            
            return np.inf
            
        except Exception as e:
            logging.error(f"收敛时间计算失败: {e}")
            return np.inf
    
    def generate_validation_report(self, metrics: ValidationMetrics) -> str:
        """生成验证报告"""
        report = f"""
=== UKF电池状态估算验证报告 ===

数据统计:
- 总数据点数: {metrics.total_points}
- 有效数据点数: {metrics.valid_points}
- 估算成功率: {metrics.estimation_success_rate:.2%}

SOC估算性能:
- 平均绝对误差 (MAE): {metrics.soc_mae:.4f}
- 均方根误差 (RMSE): {metrics.soc_rmse:.4f}
- 最大误差: {metrics.soc_max_error:.4f}
- 相关系数: {metrics.soc_correlation:.4f}

欧姆内阻估算性能:
- 平均绝对误差 (MAE): {metrics.r0_mae:.6f} Ω
- 均方根误差 (RMSE): {metrics.r0_rmse:.6f} Ω
- 最大误差: {metrics.r0_max_error:.6f} Ω
- 相关系数: {metrics.r0_correlation:.4f}

电压预测性能:
- 平均绝对误差 (MAE): {metrics.voltage_mae:.4f} V
- 均方根误差 (RMSE): {metrics.voltage_rmse:.4f} V
- 最大误差: {metrics.voltage_max_error:.4f} V

系统性能:
- 收敛时间: {metrics.convergence_time:.1f} s
- 计算效率: {metrics.computational_efficiency:.2f}

=== 性能评级 ===
"""
        
        # 添加性能评级
        if metrics.soc_rmse < 0.02:
            report += "SOC估算: 优秀 (RMSE < 2%)\n"
        elif metrics.soc_rmse < 0.05:
            report += "SOC估算: 良好 (RMSE < 5%)\n"
        else:
            report += "SOC估算: 需要改进 (RMSE ≥ 5%)\n"
        
        if metrics.r0_rmse < 0.001:
            report += "内阻估算: 优秀 (RMSE < 1mΩ)\n"
        elif metrics.r0_rmse < 0.005:
            report += "内阻估算: 良好 (RMSE < 5mΩ)\n"
        else:
            report += "内阻估算: 需要改进 (RMSE ≥ 5mΩ)\n"
        
        if metrics.voltage_rmse < 0.01:
            report += "电压预测: 优秀 (RMSE < 10mV)\n"
        elif metrics.voltage_rmse < 0.05:
            report += "电压预测: 良好 (RMSE < 50mV)\n"
        else:
            report += "电压预测: 需要改进 (RMSE ≥ 50mV)\n"
        
        return report
    
    def get_validation_history(self) -> List[Dict]:
        """获取验证历史"""
        return self.validation_history.copy()
    
    def reset(self):
        """重置验证器"""
        self.validation_history.clear()
        logging.info("估算验证器已重置")

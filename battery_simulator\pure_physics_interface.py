"""
纯净的电池包物理仿真接口

提供基于PyBaMM的纯净电池包物理仿真，只输出真实的物理数据，
专注于电池包级别的电化学和热力学仿真，不包含任何异常注入。
"""

import numpy as np
import pandas as pd
import yaml
import logging
import time
import threading
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from pathlib import Path
from queue import Queue, Empty

from pack_simulator import BatteryPackSimulator, PackState
from pack_topology import PackTopology, StandardPackTopologies
from cell_simulator import CellState


@dataclass
class PackSensorData:
    """
    电池包传感器数据格式
    
    包含包级和电芯级的完整物理数据
    """
    timestamp: float
    
    # 包级数据
    pack_voltage: float         # 包电压 (V)
    pack_current: float         # 包电流 (A)
    pack_soc: float            # 包SOC (0-1)
    pack_power: float          # 包功率 (W)
    pack_temperature: float     # 包平均温度 (°C)
    
    # 电芯级数据
    cell_voltages: List[float]      # 各电芯电压 (V)
    cell_currents: List[float]      # 各电芯电流 (A)
    cell_temperatures: List[float]  # 各电芯温度 (°C)
    cell_socs: List[float]         # 各电芯SOC (0-1)
    
    # 统计数据
    max_cell_temperature: float = 0.0
    min_cell_temperature: float = 0.0
    cell_voltage_std: float = 0.0
    cell_temperature_std: float = 0.0
    
    # 数据质量
    is_valid: bool = True
    simulation_quality: float = 1.0
    
    def get_cell_data(self, cell_id: int) -> Optional[Dict[str, float]]:
        """获取指定电芯的数据"""
        if 0 <= cell_id < len(self.cell_voltages):
            return {
                'voltage': self.cell_voltages[cell_id],
                'current': self.cell_currents[cell_id],
                'temperature': self.cell_temperatures[cell_id],
                'soc': self.cell_socs[cell_id]
            }
        return None


class PureBatteryPackSystem:
    """
    纯净电池包物理系统
    
    基于PyBaMM的纯净电池包物理仿真系统，提供：
    - 高精度电化学仿真
    - 电芯间电气和热耦合
    - 包级和电芯级数据输出
    - 可配置的包拓扑结构
    - 个体差异建模
    
    注意：此系统只提供纯净的物理数据，不包含异常注入
    """
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化纯净电池包系统
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config(config_path)
        
        # 核心组件
        self.pack_simulator: Optional[BatteryPackSimulator] = None
        self.pack_topology: Optional[PackTopology] = None
        
        # 仿真状态
        self.is_running = False
        self.current_time = 0.0
        self.start_timestamp = time.time()

        # 外部电流输入
        self.external_current = 0.0  # 外部输入的电流值
        self.use_external_current = True  # 是否使用外部电流

        # 数据缓存
        self.data_buffer = Queue(maxsize=1000)
        
        # 回调函数
        self.data_callbacks: List[Callable[[PackSensorData], None]] = []
        
        # 线程控制
        self.simulation_thread = None
        self.stop_event = threading.Event()
        
        logging.info(f"纯净电池包系统初始化")
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logging.error(f"加载配置文件失败: {e}")
            # 返回默认配置
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'simulation': {
                'duration_hours': 24,
                'time_step': 1.0
            },
            'pack_topology': {
                'type': '16s1p_linear',
                'series_count': 16,
                'parallel_count': 1
            },
            'pybamm_model': {
                'chemistry': 'LiFePO4',
                'model_type': 'SPM',
                'thermal_effects': True
            },
            'individual_variations': {
                'enable': True,
                'capacity_std': 0.02,
                'resistance_std': 0.05,
                'thermal_std': 0.03
            },
            'charge_discharge': {
                'pattern_type': 'sinusoidal',
                'sinusoidal': {
                    'cycle_duration': 4,
                    'charge_amplitude': 50,
                    'discharge_amplitude': 80
                }
            }
        }
    
    def initialize(self) -> None:
        """初始化电池包仿真系统"""
        try:
            # 创建包拓扑
            topology_config = self.config.get('pack_topology', {})
            topology_type = topology_config.get('type', '16s1p_linear')
            
            if topology_type == '16s1p_linear':
                self.pack_topology = StandardPackTopologies.create_16s1p_linear()
            elif topology_type == '16s2p_matrix':
                self.pack_topology = StandardPackTopologies.create_16s2p_matrix()
            else:
                # 自定义拓扑
                series_count = topology_config.get('series_count', 16)
                parallel_count = topology_config.get('parallel_count', 1)
                self.pack_topology = StandardPackTopologies.create_custom_pack(
                    series_count, parallel_count
                )
            
            # 验证拓扑
            validation = self.pack_topology.validate_topology()
            if not validation['is_valid']:
                raise RuntimeError(f"包拓扑验证失败: {validation['errors']}")
            
            # 创建包仿真器
            pybamm_config = self.config.get('pybamm_model', {})
            self.pack_simulator = BatteryPackSimulator(
                topology=self.pack_topology,
                chemistry=pybamm_config.get('chemistry', 'LiFePO4'),
                model_type=pybamm_config.get('model_type', 'SPM'),
                include_thermal=pybamm_config.get('thermal_effects', True)
            )
            
            # 应用个体差异
            if self.config.get('individual_variations', {}).get('enable', True):
                self._apply_individual_variations()
            
            # 设置初始条件
            self.pack_simulator.set_initial_conditions(
                initial_soc=0.5,
                initial_temperature=25.0
            )
            
            logging.info("纯净电池包系统初始化完成")
            
        except Exception as e:
            logging.error(f"初始化失败: {e}")
            raise
    
    def _apply_individual_variations(self) -> None:
        """应用个体差异参数"""
        variations_config = self.config.get('individual_variations', {})
        
        capacity_std = variations_config.get('capacity_std', 0.02)
        resistance_std = variations_config.get('resistance_std', 0.05)
        thermal_std = variations_config.get('thermal_std', 0.03)
        
        # 为每个电芯生成随机变异
        np.random.seed(42)  # 确保可重复性
        
        variations_list = []
        for cell_id in range(self.pack_topology.total_cells):
            variations = {
                'capacity_factor': 1.0 + np.random.normal(0, capacity_std),
                'resistance_factor': 1.0 + np.random.normal(0, resistance_std),
                'thermal_factor': 1.0 + np.random.normal(0, thermal_std)
            }
            variations_list.append(variations)
        
        self.pack_simulator.apply_individual_variations(variations_list)
        logging.info("个体差异参数应用完成")
    
    def start_real_time_simulation(self, speed_factor: float = 1.0) -> None:
        """
        启动实时仿真
        
        Args:
            speed_factor: 仿真速度倍数
        """
        if self.is_running:
            logging.warning("仿真已在运行")
            return
        
        if not self.pack_simulator:
            raise RuntimeError("请先调用initialize()初始化系统")
        
        self.is_running = True
        self.stop_event.clear()
        self.current_time = 0.0
        self.start_timestamp = time.time()
        
        # 启动仿真线程
        self.simulation_thread = threading.Thread(
            target=self._simulation_loop,
            args=(speed_factor,),
            daemon=True
        )
        self.simulation_thread.start()
        
        logging.info(f"实时仿真已启动 (速度倍数: {speed_factor}x)")
    
    def _simulation_loop(self, speed_factor: float) -> None:
        """仿真主循环"""
        time_step = self.config['simulation']['time_step']
        duration = self.config['simulation']['duration_hours'] * 3600
        
        # 生成电流曲线
        current_profile = self._generate_current_profile()
        
        step_count = 0
        while self.is_running and self.current_time < duration:
            if self.stop_event.is_set():
                break
            
            # 获取当前电流和环境温度
            pack_current = self._get_current_at_time(current_profile, self.current_time)
            ambient_temp = 25.0 + 5.0 * np.sin(2 * np.pi * self.current_time / (24 * 3600))  # 日温度变化
            
            # 仿真一步
            pack_state = self.pack_simulator.simulate_pack_step(
                pack_current, ambient_temp, time_step
            )
            
            # 转换为传感器数据格式
            sensor_data = self._convert_to_sensor_data(pack_state)
            
            # 添加到缓冲区
            try:
                self.data_buffer.put_nowait(sensor_data)
            except:
                # 缓冲区满，移除最老的数据
                try:
                    self.data_buffer.get_nowait()
                    self.data_buffer.put_nowait(sensor_data)
                except Empty:
                    pass
            
            # 调用回调函数
            for callback in self.data_callbacks:
                try:
                    callback(sensor_data)
                except Exception as e:
                    logging.error(f"数据回调函数执行失败: {e}")
            
            # 更新时间
            self.current_time += time_step
            step_count += 1
            
            # 控制仿真速度
            real_time_step = time_step / speed_factor
            time.sleep(real_time_step)
            
            # 定期日志
            if step_count % 60 == 0:
                logging.debug(f"仿真进行中: {self.current_time/3600:.1f}小时, "
                            f"包电压={pack_state.pack_voltage:.2f}V, "
                            f"包电流={pack_current:.1f}A")
        
        self.is_running = False
        logging.info("仿真循环结束")
    
    def _generate_current_profile(self) -> np.ndarray:
        """生成电流曲线"""
        charge_config = self.config.get('charge_discharge', {})
        pattern_type = charge_config.get('pattern_type', 'sinusoidal')
        
        duration = self.config['simulation']['duration_hours'] * 3600
        time_step = self.config['simulation']['time_step']
        time_points = np.arange(0, duration, time_step)
        
        if pattern_type == 'sinusoidal':
            sin_config = charge_config.get('sinusoidal', {})
            cycle_duration = sin_config.get('cycle_duration', 4) * 3600
            charge_amp = sin_config.get('charge_amplitude', 50)
            discharge_amp = sin_config.get('discharge_amplitude', 80)
            
            frequency = 2 * np.pi / cycle_duration
            current_profile = []
            
            for t in time_points:
                sin_value = np.sin(frequency * t)
                if sin_value >= 0:
                    current = sin_value * charge_amp
                else:
                    current = sin_value * discharge_amp
                current_profile.append(current)
            
            return np.array(current_profile)
        
        else:
            # 默认恒流
            return np.zeros_like(time_points)
    
    def _get_current_at_time(self, current_profile: np.ndarray, current_time: float) -> float:
        """获取指定时间的电流值"""
        # 如果启用外部电流输入，直接返回外部电流
        if self.use_external_current:
            return self.external_current

        # 否则使用内部生成的电流曲线
        time_step = self.config['simulation']['time_step']
        index = int(current_time / time_step)

        if 0 <= index < len(current_profile):
            return current_profile[index]
        return 0.0
    
    def _convert_to_sensor_data(self, pack_state: PackState) -> PackSensorData:
        """转换为传感器数据格式"""
        return PackSensorData(
            timestamp=self.start_timestamp + self.current_time,
            pack_voltage=pack_state.pack_voltage,
            pack_current=pack_state.pack_current,
            pack_soc=pack_state.pack_soc,
            pack_power=pack_state.pack_power,
            pack_temperature=pack_state.pack_temperature,
            cell_voltages=[state.voltage for state in pack_state.cell_states],
            cell_currents=[state.current for state in pack_state.cell_states],
            cell_temperatures=[state.temperature for state in pack_state.cell_states],
            cell_socs=[state.soc for state in pack_state.cell_states],
            max_cell_temperature=pack_state.max_cell_temperature,
            min_cell_temperature=pack_state.min_cell_temperature,
            cell_voltage_std=pack_state.cell_voltage_std,
            cell_temperature_std=pack_state.cell_temperature_std,
            is_valid=pack_state.is_valid,
            simulation_quality=pack_state.simulation_quality
        )
    
    def get_real_time_data(self) -> Optional[PackSensorData]:
        """获取实时传感器数据"""
        try:
            return self.data_buffer.get_nowait()
        except Empty:
            return None
    
    def register_data_callback(self, callback: Callable[[PackSensorData], None]) -> None:
        """注册数据更新回调函数"""
        self.data_callbacks.append(callback)

    def set_external_current(self, current: float) -> None:
        """
        设置外部电流输入

        Args:
            current: 电流值 (A, 正为充电，负为放电)
        """
        self.external_current = current

    def enable_external_current(self, enable: bool = True) -> None:
        """
        启用/禁用外部电流输入

        Args:
            enable: 是否启用外部电流输入
        """
        self.use_external_current = enable
    
    def stop_simulation(self) -> None:
        """停止仿真"""
        if not self.is_running:
            return
        
        self.is_running = False
        self.stop_event.set()
        
        if self.simulation_thread and self.simulation_thread.is_alive():
            self.simulation_thread.join(timeout=5.0)
        
        logging.info("仿真已停止")
    
    def export_simulation_data(self, output_path: str) -> None:
        """
        导出仿真数据
        
        Args:
            output_path: 输出文件路径
        """
        if not self.pack_simulator:
            logging.warning("没有仿真数据可导出")
            return
        
        # 运行完整仿真并导出
        logging.info("开始导出完整仿真数据...")
        
        # 重置仿真器
        self.pack_simulator.reset()
        
        # 生成数据
        export_data = []
        current_profile = self._generate_current_profile()
        
        time_step = self.config['simulation']['time_step']
        duration = self.config['simulation']['duration_hours'] * 3600
        
        for t in np.arange(0, duration, time_step):
            pack_current = self._get_current_at_time(current_profile, t)
            ambient_temp = 25.0 + 5.0 * np.sin(2 * np.pi * t / (24 * 3600))
            
            pack_state = self.pack_simulator.simulate_pack_step(
                pack_current, ambient_temp, time_step
            )
            
            # 添加包级数据
            row = {
                'timestamp': t,
                'pack_voltage': pack_state.pack_voltage,
                'pack_current': pack_state.pack_current,
                'pack_soc': pack_state.pack_soc,
                'pack_power': pack_state.pack_power,
                'pack_temperature': pack_state.pack_temperature
            }
            
            # 添加电芯级数据
            for i, cell_state in enumerate(pack_state.cell_states):
                row[f'cell_{i}_voltage'] = cell_state.voltage
                row[f'cell_{i}_current'] = cell_state.current
                row[f'cell_{i}_temperature'] = cell_state.temperature
                row[f'cell_{i}_soc'] = cell_state.soc
            
            export_data.append(row)
        
        # 导出为CSV
        df = pd.DataFrame(export_data)
        df.to_csv(output_path, index=False)
        
        logging.info(f"仿真数据已导出到: {output_path}")
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        info = {
            'system_type': 'Pure Battery Pack Physics System',
            'pybamm_based': True,
            'anomaly_injection': False,
            'current_time': self.current_time,
            'is_running': self.is_running
        }
        
        if self.pack_topology:
            info['pack_topology'] = self.pack_topology.export_topology_info()
        
        if self.pack_simulator:
            info['pack_info'] = self.pack_simulator.get_pack_info()
        
        return info
    
    def __enter__(self):
        """上下文管理器入口"""
        self.initialize()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop_simulation()

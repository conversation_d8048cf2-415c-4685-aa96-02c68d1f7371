<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ca6b9fe4-666e-4d30-86a7-f79ab5722bc0" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="30IoqBoo0Cn9I0X9jcy14H1YmPn" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Python.adaptive_ukf_validation.executor": "Run",
    "Python.bugfixed_ukf_validation.executor": "Run",
    "Python.complete_anomaly_verification.executor": "Run",
    "Python.debug_capacity_anomaly.executor": "Run",
    "Python.delete_dot_files.executor": "Run",
    "Python.demo_anomaly_injection_with_pybamm.executor": "Run",
    "Python.diagnose_ukf_issues.executor": "Run",
    "Python.enhanced_ukf_validation.executor": "Run",
    "Python.extract_pybamm_ocv.executor": "Run",
    "Python.full_range_ukf_validation.executor": "Run",
    "Python.improved_ukf_validation.executor": "Run",
    "Python.optimized_ukf_validation.executor": "Run",
    "Python.run_parameter_injection_examples.executor": "Run",
    "Python.run_ukf_estimation_demo.executor": "Run",
    "Python.simple_ukf_validation.executor": "Run",
    "Python.test_improved_ukf.executor": "Run",
    "Python.visual_anomaly_injection_demo.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "last_opened_file_path": "G:/电芯异常预警/v3",
    "settings.editor.selected.configurable": "com.augmentcode.intellij.settings"
  }
}]]></component>
  <component name="RunManager" selected="Python.full_range_ukf_validation">
    <configuration name="adaptive_ukf_validation" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="v3" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/adaptive_ukf_validation.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="bugfixed_ukf_validation" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="v3" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/bugfixed_ukf_validation.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="enhanced_ukf_validation" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="v3" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/enhanced_ukf_validation.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="full_range_ukf_validation" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="v3" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/full_range_ukf_validation.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="optimized_ukf_validation" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="v3" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/optimized_ukf_validation.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.full_range_ukf_validation" />
        <item itemvalue="Python.adaptive_ukf_validation" />
        <item itemvalue="Python.enhanced_ukf_validation" />
        <item itemvalue="Python.bugfixed_ukf_validation" />
        <item itemvalue="Python.optimized_ukf_validation" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-598b0484d0b5-e2d783800521-com.jetbrains.pycharm.community.sharedIndexes.bundled-PC-251.26927.74" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="ca6b9fe4-666e-4d30-86a7-f79ab5722bc0" name="更改" comment="" />
      <created>1753324636476</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753324636476</updated>
    </task>
    <servers />
  </component>
</project>
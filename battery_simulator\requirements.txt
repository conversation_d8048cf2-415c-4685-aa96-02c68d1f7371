# PyBaMM虚拟电池系统依赖包

# 核心依赖
pybamm>=23.5                    # PyBaMM电池建模库
numpy>=1.21.0                   # 数值计算
pandas>=1.3.0                   # 数据处理
pyyaml>=6.0                     # YAML配置文件解析
scipy>=1.7.0                    # 科学计算

# 可选依赖 - 可视化
matplotlib>=3.5.0               # 绘图库
seaborn>=0.11.0                 # 统计可视化

# 可选依赖 - 性能优化
numba>=0.56.0                   # JIT编译加速
h5py>=3.1.0                     # HDF5文件格式支持

# 可选依赖 - Web接口
flask>=2.0.0                    # Web框架 (如果启用REST API)
flask-socketio>=5.0.0           # WebSocket支持

# 可选依赖 - 并行计算
joblib>=1.1.0                   # 并行处理
multiprocessing-logging>=0.3.0  # 多进程日志

# 开发和测试依赖
pytest>=6.0.0                   # 测试框架
pytest-cov>=2.12.0              # 测试覆盖率
black>=22.0.0                   # 代码格式化
flake8>=4.0.0                   # 代码检查

# 文档生成
sphinx>=4.0.0                   # 文档生成
sphinx-rtd-theme>=1.0.0         # 文档主题

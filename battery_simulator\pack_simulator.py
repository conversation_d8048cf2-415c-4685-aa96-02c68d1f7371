"""
电池包级别PyBaMM仿真器

基于单电芯仿真器和包拓扑结构，实现电池包级别的高精度仿真。
包含电芯间的电气耦合、热耦合和电流分配。
"""

import numpy as np
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field

from pack_topology import PackTopology, CellConnectionType
from cell_simulator import CellSimulator, CellState
from bms import BatteryManagementSystem, BMSStatus


@dataclass
class PackState:
    """电池包状态数据结构"""
    timestamp: float
    
    # 包级电气参数
    pack_voltage: float         # 包电压 (V)
    pack_current: float         # 包电流 (A)
    pack_soc: float            # 包平均SOC (0-1)
    pack_power: float          # 包功率 (W)
    
    # 包级热参数
    pack_temperature: float     # 包平均温度 (°C)
    max_cell_temperature: float # 最高电芯温度 (°C)
    min_cell_temperature: float # 最低电芯温度 (°C)
    temperature_difference: float # 温差 (°C)
    
    # 电芯数据
    cell_states: List[CellState] = field(default_factory=list)
    
    # 包级统计
    cell_voltage_std: float = 0.0      # 电芯电压标准差
    cell_temperature_std: float = 0.0  # 电芯温度标准差

    # BMS状态
    bms_status: Optional[BMSStatus] = None
    bms_protection_active: bool = False
    actual_current: float = 0.0  # BMS限制后的实际电流

    # 数据质量
    is_valid: bool = True
    simulation_quality: float = 1.0


class PackThermalModel:
    """电池包热模型"""
    
    def __init__(self, topology: PackTopology):
        """初始化热模型"""
        self.topology = topology
        self.thermal_matrix = topology.build_thermal_matrix()
        self.cell_thermal_masses = np.full(topology.total_cells, 500.0)  # J/K
        self.ambient_temperature = 25.0  # °C
        
        logging.debug(f"包热模型初始化: {topology.total_cells}个电芯")
    
    def update_thermal_coupling(self, cell_states: List[CellState], dt: float) -> None:
        """更新电芯间热耦合"""
        if len(cell_states) != self.topology.total_cells:
            return
        
        # 当前温度向量
        temperatures = np.array([state.temperature for state in cell_states])
        heat_generation = np.array([state.heat_generation_rate for state in cell_states])
        
        # 计算热传导
        heat_transfer = np.zeros(self.topology.total_cells)
        
        for i in range(self.topology.total_cells):
            for j in range(self.topology.total_cells):
                if i != j and self.thermal_matrix[i, j] != np.inf:
                    # 热传导: Q = (T_j - T_i) / R_thermal
                    heat_flow = (temperatures[j] - temperatures[i]) / self.thermal_matrix[i, j]
                    heat_transfer[i] += heat_flow
        
        # 环境散热
        ambient_heat_loss = (temperatures - self.ambient_temperature) / 2.0  # 简化散热模型
        
        # 更新温度
        for i, state in enumerate(cell_states):
            net_heat = heat_generation[i] + heat_transfer[i] - ambient_heat_loss[i]
            dtemp = net_heat * dt / self.cell_thermal_masses[i]
            state.temperature += dtemp
    
    def set_ambient_temperature(self, temperature: float) -> None:
        """设置环境温度"""
        self.ambient_temperature = temperature
    
    def get_pack_temperature_stats(self, cell_states: List[CellState]) -> Dict[str, float]:
        """获取包温度统计"""
        if not cell_states:
            return {'average': 25.0, 'max': 25.0, 'min': 25.0, 'std': 0.0}
        
        temperatures = [state.temperature for state in cell_states]
        
        return {
            'average': np.mean(temperatures),
            'max': np.max(temperatures),
            'min': np.min(temperatures),
            'std': np.std(temperatures)
        }


class PackElectricalModel:
    """电池包电气模型"""
    
    def __init__(self, topology: PackTopology):
        """初始化电气模型"""
        self.topology = topology
        self.electrical_matrix = topology.build_electrical_matrix()
        self.series_groups = topology.get_series_groups()
        self.parallel_groups = topology.get_parallel_groups()
        
        logging.debug(f"包电气模型初始化: {len(self.series_groups)}个串联组, "
                     f"{len(self.parallel_groups)}个并联组")
    
    def distribute_current(self, pack_current: float, 
                          cell_resistances: List[float]) -> List[float]:
        """
        将包电流分配到各个电芯
        
        Args:
            pack_current: 包总电流 (A)
            cell_resistances: 各电芯内阻列表 (Ω)
            
        Returns:
            各电芯电流列表 (A)
        """
        cell_currents = np.zeros(self.topology.total_cells)
        
        if self.topology.parallel_count == 1:
            # 纯串联：所有电芯电流相同
            cell_currents.fill(pack_current)
        else:
            # 有并联：需要根据内阻分配电流
            for parallel_group in self.parallel_groups:
                if len(parallel_group) > 1:
                    # 计算并联支路电流分配
                    group_resistances = [cell_resistances[i] for i in parallel_group]
                    total_conductance = sum(1.0/r if r > 0 else 1e6 for r in group_resistances)
                    
                    for i, cell_id in enumerate(parallel_group):
                        if cell_resistances[cell_id] > 0:
                            conductance = 1.0 / cell_resistances[cell_id]
                            current_fraction = conductance / total_conductance
                            cell_currents[cell_id] = pack_current * current_fraction
                        else:
                            cell_currents[cell_id] = pack_current / len(parallel_group)
                else:
                    # 单个电芯
                    cell_currents[parallel_group[0]] = pack_current
        
        return cell_currents.tolist()
    
    def calculate_pack_voltage(self, cell_states: List[CellState]) -> float:
        """
        计算包电压
        
        Args:
            cell_states: 电芯状态列表
            
        Returns:
            包电压 (V)
        """
        if not cell_states or len(cell_states) != self.topology.total_cells:
            return 0.0
        
        # 串联电压相加
        pack_voltage = 0.0
        
        for series_group in self.series_groups:
            # 每个串联组的电压
            group_voltage = 0.0
            for cell_id in series_group:
                if cell_id < len(cell_states):
                    group_voltage += cell_states[cell_id].voltage
            
            # 并联组取平均 (简化处理)
            pack_voltage = max(pack_voltage, group_voltage)
        
        return pack_voltage
    
    def calculate_pack_soc(self, cell_states: List[CellState]) -> float:
        """计算包SOC (加权平均)"""
        if not cell_states:
            return 0.0
        
        # 简化：直接平均
        socs = [state.soc for state in cell_states]
        return np.mean(socs)
    
    def get_pack_electrical_stats(self, cell_states: List[CellState]) -> Dict[str, float]:
        """获取包电气统计"""
        if not cell_states:
            return {'voltage_std': 0.0, 'soc_std': 0.0, 'current_std': 0.0}
        
        voltages = [state.voltage for state in cell_states]
        socs = [state.soc for state in cell_states]
        currents = [state.current for state in cell_states]
        
        return {
            'voltage_std': np.std(voltages),
            'soc_std': np.std(socs),
            'current_std': np.std(currents)
        }


class BatteryPackSimulator:
    """
    电池包级别的PyBaMM仿真器
    
    整合单电芯仿真器、包拓扑结构、热模型和电气模型，
    提供完整的电池包级别仿真能力。
    """
    
    def __init__(self, topology: PackTopology, chemistry: str = "LiFePO4",
                 model_type: str = "SPM", include_thermal: bool = True):
        """
        初始化电池包仿真器
        
        Args:
            topology: 电池包拓扑结构
            chemistry: 电池化学体系
            model_type: PyBaMM模型类型
            include_thermal: 是否包含热效应
        """
        self.topology = topology
        self.chemistry = chemistry
        self.model_type = model_type
        self.include_thermal = include_thermal
        
        # 创建单电芯仿真器
        self.cell_simulators: List[CellSimulator] = []
        for cell_id in range(topology.total_cells):
            simulator = CellSimulator(
                cell_id=cell_id,
                chemistry=chemistry,
                model_type=model_type,
                include_thermal=include_thermal
            )
            self.cell_simulators.append(simulator)
        
        # 创建包级模型
        self.thermal_model = PackThermalModel(topology) if include_thermal else None
        self.electrical_model = PackElectricalModel(topology)

        # 创建BMS系统
        self.bms = BatteryManagementSystem(chemistry=chemistry, cell_count=topology.total_cells)

        # 仿真状态
        self.current_pack_state: Optional[PackState] = None
        self.simulation_time = 0.0
        self.is_initialized = False

        # 性能优化: 添加缓存机制
        self._state_cache = {}
        self._cache_valid = False
        self._last_update_time = 0.0
        self._cache_timeout = 0.1  # 100ms缓存超时
        self._batch_mode = False  # 批处理模式标志
        self._fast_mode = False   # 快速模式标志
        
        logging.info(f"电池包仿真器初始化: {topology.pack_name}, "
                    f"{topology.total_cells}个{chemistry}电芯")

    def enable_batch_mode(self, enable: bool = True) -> None:
        """
        启用/禁用批处理模式以提高性能

        Args:
            enable: 是否启用批处理模式
        """
        self._batch_mode = enable
        if enable:
            logging.info("电池包仿真器: 启用批处理模式 (性能优化)")
        else:
            logging.info("电池包仿真器: 禁用批处理模式")

    def enable_fast_mode(self, enable: bool = True) -> None:
        """
        启用/禁用快速模式以大幅提高性能 (牺牲一些精度)

        Args:
            enable: 是否启用快速模式
        """
        self._fast_mode = enable
        if enable:
            # 快速模式下自动启用批处理模式
            self._batch_mode = True
            # 增加缓存超时时间
            self._cache_timeout = 1.0  # 1秒缓存
            logging.info("电池包仿真器: 启用快速模式 (大幅性能优化，轻微精度损失)")
        else:
            self._cache_timeout = 0.1  # 恢复100ms缓存
            logging.info("电池包仿真器: 禁用快速模式")
    
    def apply_individual_variations(self, variations_list: Optional[List[Dict[str, float]]] = None) -> None:
        """
        为各电芯应用个体差异

        Args:
            variations_list: 每个电芯的参数变异列表，如果为None则自动生成
        """
        if variations_list is None:
            # 自动生成个体差异参数
            import numpy as np
            np.random.seed(42)  # 确保可重复性
            variations_list = []
            for i in range(len(self.cell_simulators)):
                variations = {
                    'capacity_factor': np.random.normal(1.0, 0.02),    # 2%容量差异
                    'resistance_factor': np.random.normal(1.0, 0.01),  # 1%内阻差异
                    'thermal_factor': np.random.normal(1.0, 0.005),    # 0.5%热差异
                }
                variations_list.append(variations)
            logging.info("自动生成电芯个体差异参数")

        if len(variations_list) != len(self.cell_simulators):
            raise ValueError("变异参数数量与电芯数量不匹配")

        for i, (simulator, variations) in enumerate(zip(self.cell_simulators, variations_list)):
            simulator.apply_individual_variations(variations)

        logging.info("电池包个体差异参数应用完成")
    
    def set_initial_conditions(self, initial_soc: float = 0.5, 
                             initial_temperature: float = 25.0,
                             soc_variations: Optional[List[float]] = None) -> None:
        """
        设置初始条件
        
        Args:
            initial_soc: 平均初始SOC
            initial_temperature: 初始温度
            soc_variations: 各电芯SOC差异 (可选)
        """
        if soc_variations is None:
            soc_variations = [0.0] * len(self.cell_simulators)
        
        if len(soc_variations) != len(self.cell_simulators):
            raise ValueError("SOC变异数量与电芯数量不匹配")
        
        # 设置各电芯初始条件
        for i, simulator in enumerate(self.cell_simulators):
            cell_soc = np.clip(initial_soc + soc_variations[i], 0.0, 1.0)
            simulator.set_initial_conditions(cell_soc, initial_temperature)
        
        # 设置环境温度
        if self.thermal_model:
            self.thermal_model.set_ambient_temperature(initial_temperature)
        
        # 创建初始包状态
        self._update_pack_state()
        
        self.simulation_time = 0.0
        self.is_initialized = True
        
        logging.info(f"电池包初始条件设置完成: SOC={initial_soc}, T={initial_temperature}°C")

    def _get_cell_states_fast(self) -> List:
        """
        快速获取电芯状态 - 性能优化版本

        Returns:
            电芯状态列表
        """
        cell_states = []

        if self._batch_mode and len(self.cell_simulators) > 8:
            # 批处理模式: 对大型电池包使用并行处理
            try:
                from concurrent.futures import ThreadPoolExecutor
                max_workers = min(4, len(self.cell_simulators) // 4)
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    futures = [executor.submit(sim.get_current_state) for sim in self.cell_simulators]
                    for future in futures:
                        try:
                            state = future.result(timeout=0.5)  # 500ms超时
                            if state:
                                cell_states.append(state)
                        except Exception:
                            # 超时或失败时跳过
                            pass
            except Exception:
                # 如果并行失败，回退到串行模式
                cell_states = self._get_cell_states_serial()
        else:
            # 串行模式: 逐个获取状态
            cell_states = self._get_cell_states_serial()

        return cell_states

    def _get_cell_states_serial(self) -> List:
        """串行获取电芯状态"""
        cell_states = []
        for simulator in self.cell_simulators:
            state = simulator.get_current_state()
            if state:
                cell_states.append(state)
        return cell_states

    def _simulate_cells_parallel(self, cell_currents: List[float],
                                ambient_temperature: float, dt: float) -> None:
        """
        并行执行电芯仿真 - 性能优化

        Args:
            cell_currents: 各电芯电流列表
            ambient_temperature: 环境温度
            dt: 时间步长
        """
        try:
            from concurrent.futures import ThreadPoolExecutor, as_completed

            def simulate_single_cell(args):
                i, simulator, current = args
                try:
                    simulator.simulate_step(current, ambient_temperature, dt)
                    return i, True, None
                except Exception as e:
                    return i, False, str(e)

            # 准备参数
            tasks = [(i, sim, cell_currents[i]) for i, sim in enumerate(self.cell_simulators)]

            # 并行执行 - 优化超时策略
            max_workers = min(2, len(self.cell_simulators) // 8)  # 减少并行度
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = [executor.submit(simulate_single_cell, task) for task in tasks]

                failed_cells = []
                completed_count = 0
                for future in as_completed(futures, timeout=15.0):  # 增加总超时到15秒
                    try:
                        cell_id, success, error = future.result(timeout=10.0)  # 单个任务10秒超时
                        completed_count += 1
                        if not success:
                            failed_cells.append((cell_id, error))
                    except Exception as e:
                        failed_cells.append((-1, str(e)))

                # 如果完成率太低，认为并行失败
                completion_rate = completed_count / len(tasks)
                if completion_rate < 0.8:  # 80%完成率阈值
                    raise Exception(f"并行完成率过低: {completion_rate:.1%}")

                # 如果有失败的电芯，记录警告但继续
                if failed_cells:
                    for cell_id, error in failed_cells[:3]:  # 只显示前3个错误
                        logging.warning(f"电芯{cell_id}并行仿真失败: {error}")

                    if len(failed_cells) > 3:
                        logging.warning(f"另外{len(failed_cells)-3}个电芯仿真失败")

        except Exception as e:
            # 并行失败时回退到串行模式
            logging.warning(f"并行仿真失败，回退到串行模式: {e}")
            for i, simulator in enumerate(self.cell_simulators):
                try:
                    simulator.simulate_step(cell_currents[i], ambient_temperature, dt)
                except Exception as cell_error:
                    logging.error(f"电芯{i}串行仿真也失败: {cell_error}")
    
    def simulate_pack_step(self, pack_current: float, ambient_temperature: float,
                          dt: float) -> PackState:
        """
        仿真电池包一个时间步 (集成BMS保护)

        Args:
            pack_current: 包电流 (A, 正为充电)
            ambient_temperature: 环境温度 (°C)
            dt: 时间步长 (s)

        Returns:
            新的包状态 (包含BMS状态)
        """
        if not self.is_initialized:
            raise RuntimeError("电池包仿真器未初始化")

        # 1. 获取当前包状态用于BMS检查
        self._update_pack_state()

        # 2. BMS保护检查和电流限制
        operation_allowed, actual_current, bms_status = self.bms.update_status(
            self.current_pack_state, pack_current
        )

        if not operation_allowed:
            # BMS禁止操作，但需要区分真正的保护和0电流状态
            if abs(pack_current) > 0.01:  # 只有非零电流被禁止时才记录警告
                logging.debug(f"BMS保护触发: 请求电流{pack_current:.1f}A被禁止")  # 改为debug级别
                self.current_pack_state.bms_protection_active = True
                self.current_pack_state.actual_current = 0.0
            else:
                # 0电流请求被"禁止"是正常的，不需要警告
                self.current_pack_state.bms_protection_active = False
                self.current_pack_state.actual_current = actual_current

            self.current_pack_state.bms_status = bms_status
            return self.current_pack_state

        # 3. 使用BMS限制后的电流
        if abs(actual_current - pack_current) > 0.1:
            logging.info(f"BMS电流限制: {pack_current:.1f}A -> {actual_current:.1f}A")

        # 4. 获取当前电芯内阻
        cell_resistances = []
        for simulator in self.cell_simulators:
            state = simulator.get_current_state()
            if state and state.internal_resistance is not None:
                cell_resistances.append(state.internal_resistance)
            else:
                cell_resistances.append(0.003)  # 默认内阻

        # 5. 电流分配 (使用BMS限制后的电流)
        cell_currents = self.electrical_model.distribute_current(actual_current, cell_resistances)

        # 6. 更新环境温度
        if self.thermal_model:
            self.thermal_model.set_ambient_temperature(ambient_temperature)

        # 7. 各电芯独立仿真 - 性能优化版本
        if self._batch_mode and len(self.cell_simulators) > 8:
            # 大型电池包使用并行仿真
            self._simulate_cells_parallel(cell_currents, ambient_temperature, dt)
        else:
            # 小型电池包使用串行仿真 - 添加错误处理
            failed_cells = []
            for i, simulator in enumerate(self.cell_simulators):
                try:
                    simulator.simulate_step(cell_currents[i], ambient_temperature, dt)
                except Exception as cell_error:
                    failed_cells.append((i, str(cell_error)))
                    logging.warning(f"电芯{i}仿真失败: {cell_error}")

            # 如果失败电芯过多，记录警告
            if len(failed_cells) > len(self.cell_simulators) * 0.5:
                logging.error(f"超过50%的电芯仿真失败 ({len(failed_cells)}/{len(self.cell_simulators)})")
            elif failed_cells:
                logging.info(f"{len(failed_cells)}个电芯仿真失败，但继续运行")

        # 8. 热耦合计算
        if self.thermal_model:
            cell_states = [sim.get_current_state() for sim in self.cell_simulators]
            self.thermal_model.update_thermal_coupling(cell_states, dt)

        # 9. 电芯均衡模拟
        if bms_status.balancing_active:
            # 简化的均衡效果模拟
            self.current_pack_state = self.bms.simulate_balancing_effect(
                self.current_pack_state, dt
            )

        # 10. 更新包状态
        self._update_pack_state()

        # 11. 添加BMS状态信息
        self.current_pack_state.bms_status = bms_status
        self.current_pack_state.bms_protection_active = len(bms_status.active_protections) > 0
        self.current_pack_state.actual_current = actual_current

        self.simulation_time += dt

        return self.current_pack_state
    
    def _update_pack_state(self) -> None:
        """更新包状态 - 性能优化版本"""
        import time
        current_time = time.time()

        # 性能优化: 检查缓存是否有效
        if (self._cache_valid and
            current_time - self._last_update_time < self._cache_timeout and
            self._batch_mode):
            # 使用缓存的状态
            if 'cell_states' in self._state_cache:
                cell_states = self._state_cache['cell_states']
            else:
                cell_states = self._get_cell_states_fast()
        else:
            # 重新获取状态
            cell_states = self._get_cell_states_fast()
            self._state_cache['cell_states'] = cell_states
            self._cache_valid = True
            self._last_update_time = current_time

        if not cell_states:
            return

        # 计算包级参数
        pack_voltage = self.electrical_model.calculate_pack_voltage(cell_states)
        pack_soc = self.electrical_model.calculate_pack_soc(cell_states)
        pack_current = cell_states[0].current  # 简化：取第一个电芯的电流
        pack_power = pack_voltage * pack_current
        
        # 热统计
        if self.thermal_model:
            temp_stats = self.thermal_model.get_pack_temperature_stats(cell_states)
        else:
            temp_stats = {'average': 25.0, 'max': 25.0, 'min': 25.0, 'std': 0.0}
        
        # 电气统计
        electrical_stats = self.electrical_model.get_pack_electrical_stats(cell_states)
        
        # 创建包状态
        self.current_pack_state = PackState(
            timestamp=self.simulation_time,
            pack_voltage=pack_voltage,
            pack_current=pack_current,
            pack_soc=pack_soc,
            pack_power=pack_power,
            pack_temperature=temp_stats['average'],
            max_cell_temperature=temp_stats['max'],
            min_cell_temperature=temp_stats['min'],
            temperature_difference=temp_stats['max'] - temp_stats['min'],
            cell_states=cell_states.copy(),
            cell_voltage_std=electrical_stats['voltage_std'],
            cell_temperature_std=temp_stats['std'],
            is_valid=all(state.is_valid for state in cell_states),
            simulation_quality=np.mean([state.simulation_quality for state in cell_states])
        )
    
    def get_current_pack_state(self) -> Optional[PackState]:
        """获取当前包状态"""
        return self.current_pack_state
    
    def get_cell_state(self, cell_id: int) -> Optional[CellState]:
        """获取指定电芯状态"""
        if 0 <= cell_id < len(self.cell_simulators):
            return self.cell_simulators[cell_id].get_current_state()
        return None
    
    def reset(self, initial_soc: float = 0.5, initial_temperature: float = 25.0) -> None:
        """重置电池包仿真器"""
        self.set_initial_conditions(initial_soc, initial_temperature)
        logging.info("电池包仿真器已重置")
    
    def get_pack_info(self) -> Dict[str, Any]:
        """获取电池包信息"""
        return {
            'topology_info': self.topology.export_topology_info(),
            'simulation_info': {
                'chemistry': self.chemistry,
                'model_type': self.model_type,
                'include_thermal': self.include_thermal,
                'simulation_time': self.simulation_time,
                'is_initialized': self.is_initialized
            },
            'cell_count': len(self.cell_simulators),
            'current_pack_state': {
                'voltage': self.current_pack_state.pack_voltage if self.current_pack_state else 0.0,
                'current': self.current_pack_state.pack_current if self.current_pack_state else 0.0,
                'soc': self.current_pack_state.pack_soc if self.current_pack_state else 0.0,
                'temperature': self.current_pack_state.pack_temperature if self.current_pack_state else 25.0
            }
        }

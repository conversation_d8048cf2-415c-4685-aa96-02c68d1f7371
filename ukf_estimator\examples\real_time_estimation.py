#!/usr/bin/env python3
"""
实时估算示例

演示UKF估算器的实时估算能力，包括实时数据处理、
在线参数自适应和实时可视化监控。
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt
import time
import threading
import logging
from collections import deque

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'battery_simulator'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class RealTimeEstimationDemo:
    """实时估算演示类"""
    
    def __init__(self):
        """初始化演示系统"""
        self.is_running = False
        self.data_lock = threading.Lock()
        
        # 数据缓冲区
        self.max_points = 200
        self.times = deque(maxlen=self.max_points)
        self.soc_est = deque(maxlen=self.max_points)
        self.soc_true = deque(maxlen=self.max_points)
        self.r0_est = deque(maxlen=self.max_points)
        self.r0_true = deque(maxlen=self.max_points)
        self.voltages_pred = deque(maxlen=self.max_points)
        self.voltages_meas = deque(maxlen=self.max_points)
        self.voltage_errors = deque(maxlen=self.max_points)
        
        # 性能统计
        self.estimation_count = 0
        self.start_time = None
        
    def setup_system(self):
        """设置系统组件"""
        print("🔧 设置实时估算系统...")
        
        # 导入模块
        from battery_simulator.cell_simulator import CellSimulator
        from ukf_estimator import create_default_estimator
        from ukf_estimator.data_interface import MeasurementData
        
        # 创建虚拟电池
        self.cell = CellSimulator(cell_id=1, chemistry="LiFePO4", include_thermal=True)
        self.cell.set_initial_conditions(initial_soc=0.7, initial_temperature=25.0)
        
        # 创建UKF估算器
        self.estimator = create_default_estimator(self.cell)
        self.estimator.initialize(initial_soc=0.6, initial_temperature=25.0)  # 故意偏差
        
        print("✅ 系统设置完成")
        
    def setup_realtime_plot(self):
        """设置实时绘图"""
        print("📊 设置实时可视化...")
        
        # 创建图形窗口
        self.fig, self.axes = plt.subplots(2, 2, figsize=(12, 8))
        self.fig.suptitle('UKF实时估算监控', fontsize=16, fontweight='bold')
        
        # 初始化绘图线条
        self.line_soc_est, = self.axes[0, 0].plot([], [], 'b-', linewidth=2, label='UKF估算')
        self.line_soc_true, = self.axes[0, 0].plot([], [], 'r--', linewidth=2, label='真实值')
        self.axes[0, 0].set_xlabel('时间 (s)')
        self.axes[0, 0].set_ylabel('SOC')
        self.axes[0, 0].set_title('SOC实时估算')
        self.axes[0, 0].legend()
        self.axes[0, 0].grid(True, alpha=0.3)
        self.axes[0, 0].set_xlim(0, 100)
        self.axes[0, 0].set_ylim(0, 1)
        
        self.line_r0_est, = self.axes[0, 1].plot([], [], 'g-', linewidth=2, label='UKF估算')
        self.line_r0_true, = self.axes[0, 1].plot([], [], 'r--', linewidth=2, label='真实值')
        self.axes[0, 1].set_xlabel('时间 (s)')
        self.axes[0, 1].set_ylabel('内阻 (mΩ)')
        self.axes[0, 1].set_title('内阻实时估算')
        self.axes[0, 1].legend()
        self.axes[0, 1].grid(True, alpha=0.3)
        self.axes[0, 1].set_xlim(0, 100)
        self.axes[0, 1].set_ylim(0, 10)
        
        self.line_volt_pred, = self.axes[1, 0].plot([], [], 'b-', linewidth=2, label='预测电压')
        self.line_volt_meas, = self.axes[1, 0].plot([], [], 'r-', linewidth=1, alpha=0.7, label='测量电压')
        self.axes[1, 0].set_xlabel('时间 (s)')
        self.axes[1, 0].set_ylabel('电压 (V)')
        self.axes[1, 0].set_title('电压预测')
        self.axes[1, 0].legend()
        self.axes[1, 0].grid(True, alpha=0.3)
        self.axes[1, 0].set_xlim(0, 100)
        self.axes[1, 0].set_ylim(3.0, 3.6)
        
        self.line_error, = self.axes[1, 1].plot([], [], 'purple', linewidth=2)
        self.axes[1, 1].set_xlabel('时间 (s)')
        self.axes[1, 1].set_ylabel('电压误差 (mV)')
        self.axes[1, 1].set_title('预测误差')
        self.axes[1, 1].grid(True, alpha=0.3)
        self.axes[1, 1].set_xlim(0, 100)
        self.axes[1, 1].set_ylim(0, 50)
        
        # 添加文本显示
        self.text_info = self.fig.text(0.02, 0.02, '', fontsize=10, fontfamily='monospace',
                                      bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        
        plt.tight_layout()
        plt.ion()  # 开启交互模式
        
        print("✅ 实时可视化设置完成")
        
    def data_acquisition_thread(self):
        """数据采集线程"""
        print("🔄 启动数据采集线程...")
        
        dt = 1.0  # 1秒采样间隔
        step = 0
        
        while self.is_running:
            try:
                # 生成动态电流 (模拟实际使用)
                t = step * dt
                if t < 30:
                    current = 2.0 * np.sin(2 * np.pi * t / 20)  # 正弦波
                elif t < 60:
                    current = 3.0 if (step % 10) < 5 else -1.0  # 脉冲
                else:
                    current = 1.0 + 0.5 * np.random.randn()  # 随机扰动
                
                # 虚拟电池仿真
                cell_state = self.cell.simulate_step(current, 25.0, dt)
                
                # UKF估算
                estimation = self.estimator.estimate_step(
                    voltage=cell_state.voltage,
                    current=cell_state.current,
                    temperature=cell_state.temperature,
                    dt=dt
                )
                
                # 更新数据缓冲区
                with self.data_lock:
                    self.times.append(t)
                    self.soc_est.append(estimation.soc)
                    self.soc_true.append(cell_state.soc)
                    self.r0_est.append(estimation.r0 * 1000)  # 转换为mΩ
                    self.r0_true.append(cell_state.internal_resistance * 1000)
                    self.voltages_pred.append(estimation.predicted_voltage)
                    self.voltages_meas.append(estimation.measured_voltage)
                    self.voltage_errors.append(abs(estimation.voltage_error) * 1000)  # 转换为mV
                
                self.estimation_count += 1
                step += 1
                
                # 控制采样频率
                time.sleep(dt)
                
            except Exception as e:
                logging.error(f"数据采集错误: {e}")
                break
        
        print("🛑 数据采集线程结束")
        
    def update_plot(self):
        """更新绘图"""
        if not self.times:
            return
        
        with self.data_lock:
            times_list = list(self.times)
            soc_est_list = list(self.soc_est)
            soc_true_list = list(self.soc_true)
            r0_est_list = list(self.r0_est)
            r0_true_list = list(self.r0_true)
            voltages_pred_list = list(self.voltages_pred)
            voltages_meas_list = list(self.voltages_meas)
            voltage_errors_list = list(self.voltage_errors)
        
        # 更新数据
        self.line_soc_est.set_data(times_list, soc_est_list)
        self.line_soc_true.set_data(times_list, soc_true_list)
        self.line_r0_est.set_data(times_list, r0_est_list)
        self.line_r0_true.set_data(times_list, r0_true_list)
        self.line_volt_pred.set_data(times_list, voltages_pred_list)
        self.line_volt_meas.set_data(times_list, voltages_meas_list)
        self.line_error.set_data(times_list, voltage_errors_list)
        
        # 动态调整坐标轴
        if times_list:
            current_time = times_list[-1]
            window_size = 100  # 显示最近100秒
            
            for ax in self.axes.flat:
                ax.set_xlim(max(0, current_time - window_size), current_time + 10)
            
            # 动态调整Y轴
            if soc_est_list:
                soc_min = min(min(soc_est_list), min(soc_true_list)) - 0.05
                soc_max = max(max(soc_est_list), max(soc_true_list)) + 0.05
                self.axes[0, 0].set_ylim(max(0, soc_min), min(1, soc_max))
            
            if r0_est_list:
                r0_min = min(min(r0_est_list), min(r0_true_list)) - 0.5
                r0_max = max(max(r0_est_list), max(r0_true_list)) + 0.5
                self.axes[0, 1].set_ylim(max(0, r0_min), r0_max)
            
            if voltages_pred_list:
                v_min = min(min(voltages_pred_list), min(voltages_meas_list)) - 0.05
                v_max = max(max(voltages_pred_list), max(voltages_meas_list)) + 0.05
                self.axes[1, 0].set_ylim(v_min, v_max)
            
            if voltage_errors_list:
                error_max = max(voltage_errors_list) + 5
                self.axes[1, 1].set_ylim(0, error_max)
        
        # 更新信息文本
        if self.estimation_count > 0:
            elapsed_time = time.time() - self.start_time if self.start_time else 0
            estimation_rate = self.estimation_count / elapsed_time if elapsed_time > 0 else 0
            
            # 计算当前性能指标
            if len(soc_est_list) > 10:
                recent_soc_error = np.mean([abs(e - t) for e, t in 
                                          zip(soc_est_list[-10:], soc_true_list[-10:])])
                recent_r0_error = np.mean([abs(e - t) for e, t in 
                                         zip(r0_est_list[-10:], r0_true_list[-10:])])
                recent_voltage_error = np.mean(voltage_errors_list[-10:])
            else:
                recent_soc_error = recent_r0_error = recent_voltage_error = 0
            
            info_text = f"""实时估算状态:
估算次数: {self.estimation_count}
运行时间: {elapsed_time:.1f}s
估算频率: {estimation_rate:.1f} Hz

当前性能 (最近10点):
SOC误差: {recent_soc_error:.3f}
R0误差: {recent_r0_error:.2f} mΩ
电压误差: {recent_voltage_error:.1f} mV"""
            
            self.text_info.set_text(info_text)
        
        # 重绘
        self.fig.canvas.draw()
        self.fig.canvas.flush_events()
        
    def run_realtime_demo(self, duration: float = 120.0):
        """运行实时演示"""
        print(f"🚀 启动实时估算演示 (持续{duration}秒)...")
        
        self.is_running = True
        self.start_time = time.time()
        
        # 启动数据采集线程
        data_thread = threading.Thread(target=self.data_acquisition_thread)
        data_thread.daemon = True
        data_thread.start()
        
        # 主线程负责绘图更新
        try:
            end_time = time.time() + duration
            while time.time() < end_time and self.is_running:
                self.update_plot()
                time.sleep(0.1)  # 10Hz更新频率
                
                # 检查窗口是否关闭
                if not plt.get_fignums():
                    break
                    
        except KeyboardInterrupt:
            print("\n⚠️  用户中断演示")
        except Exception as e:
            print(f"❌ 演示运行错误: {e}")
        finally:
            self.is_running = False
            
        print("🛑 实时演示结束")
        
        # 等待数据线程结束
        data_thread.join(timeout=2.0)
        
        # 保存最终结果
        self.save_results()
        
    def save_results(self):
        """保存结果"""
        print("💾 保存实时估算结果...")
        
        try:
            # 保存最终图像
            plt.savefig("realtime_estimation_final.png", dpi=300, bbox_inches='tight')
            
            # 保存数据
            with self.data_lock:
                if self.times:
                    data = {
                        'times': list(self.times),
                        'soc_est': list(self.soc_est),
                        'soc_true': list(self.soc_true),
                        'r0_est': list(self.r0_est),
                        'r0_true': list(self.r0_true),
                        'voltage_errors': list(self.voltage_errors)
                    }
                    
                    np.savez('realtime_estimation_data.npz', **data)
                    
                    # 计算最终统计
                    soc_errors = [abs(e - t) for e, t in zip(data['soc_est'], data['soc_true'])]
                    r0_errors = [abs(e - t) for e, t in zip(data['r0_est'], data['r0_true'])]
                    
                    stats = f"""实时估算最终统计:

数据点数: {len(data['times'])}
运行时间: {data['times'][-1] - data['times'][0]:.1f} 秒
平均估算频率: {len(data['times']) / (data['times'][-1] - data['times'][0]):.1f} Hz

SOC估算性能:
- 平均绝对误差: {np.mean(soc_errors):.4f}
- 均方根误差: {np.sqrt(np.mean([e**2 for e in soc_errors])):.4f}
- 最大误差: {np.max(soc_errors):.4f}

内阻估算性能:
- 平均绝对误差: {np.mean(r0_errors):.2f} mΩ
- 均方根误差: {np.sqrt(np.mean([e**2 for e in r0_errors])):.2f} mΩ
- 最大误差: {np.max(r0_errors):.2f} mΩ

电压预测性能:
- 平均误差: {np.mean(data['voltage_errors']):.1f} mV
- 最大误差: {np.max(data['voltage_errors']):.1f} mV
"""
                    
                    with open("realtime_estimation_stats.txt", "w", encoding="utf-8") as f:
                        f.write(stats)
                    
                    print("✅ 结果已保存:")
                    print("  - realtime_estimation_final.png")
                    print("  - realtime_estimation_data.npz")
                    print("  - realtime_estimation_stats.txt")
                    
        except Exception as e:
            print(f"⚠️  保存结果失败: {e}")


def main():
    """主函数"""
    print("⚡ UKF实时估算演示")
    print("=" * 60)
    
    try:
        # 创建演示系统
        demo = RealTimeEstimationDemo()
        
        # 设置系统
        demo.setup_system()
        demo.setup_realtime_plot()
        
        print("\n📋 演示说明:")
        print("- 实时显示SOC和内阻估算结果")
        print("- 动态电流激励信号")
        print("- 实时性能监控")
        print("- 按Ctrl+C或关闭窗口结束演示")
        
        input("\n按Enter键开始实时演示...")
        
        # 运行演示
        demo.run_realtime_demo(duration=120.0)  # 2分钟演示
        
        print("\n🎯 实时估算演示完成!")
        print("📊 查看保存的结果文件了解详细性能")
        
        # 保持图形显示
        plt.ioff()
        plt.show()
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        logging.error(f"演示失败: {e}", exc_info=True)
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 实时估算演示成功!")
    else:
        print("\n❌ 实时估算演示失败!")
        sys.exit(1)

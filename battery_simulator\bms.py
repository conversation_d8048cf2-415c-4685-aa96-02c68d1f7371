"""
电池管理系统 (BMS) - Battery Management System

模拟现实中的BMS系统，提供电池包的安全保护、状态监控和管理功能。
包括过充过放保护、温度保护、电流限制、电芯均衡等核心功能。
"""

import logging
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import time

# 避免循环导入，使用类型注解
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from pack_simulator import PackState


class BMSProtectionLevel(Enum):
    """BMS保护级别"""
    NORMAL = "normal"           # 正常状态
    WARNING = "warning"         # 警告状态
    PROTECTION = "protection"   # 保护状态
    FAULT = "fault"            # 故障状态


class BMSProtectionType(Enum):
    """BMS保护类型"""
    OVERVOLTAGE = "overvoltage"                 # 过压保护
    UNDERVOLTAGE = "undervoltage"               # 欠压保护
    OVERCURRENT_CHARGE = "overcurrent_charge"   # 充电过流保护
    OVERCURRENT_DISCHARGE = "overcurrent_discharge"  # 放电过流保护
    OVERTEMPERATURE = "overtemperature"         # 过温保护
    UNDERTEMPERATURE = "undertemperature"       # 低温保护
    CELL_IMBALANCE = "cell_imbalance"          # 电芯不均衡
    SOC_HIGH = "soc_high"                      # SOC过高
    SOC_LOW = "soc_low"                        # SOC过低


@dataclass
class BMSProtectionEvent:
    """BMS保护事件"""
    timestamp: float
    protection_type: BMSProtectionType
    protection_level: BMSProtectionLevel
    affected_cells: List[int]
    values: Dict[str, float]
    description: str
    action_taken: str


@dataclass
class BMSStatus:
    """BMS状态"""
    timestamp: float
    system_status: BMSProtectionLevel
    active_protections: List[BMSProtectionEvent]
    charge_allowed: bool
    discharge_allowed: bool
    max_charge_current: float
    max_discharge_current: float
    balancing_active: bool
    balancing_cells: List[int]
    estimated_soc: float
    estimated_soh: float  # State of Health


class BatteryManagementSystem:
    """
    电池管理系统 (BMS)
    
    模拟现实中的BMS功能，包括：
    - 电压保护 (过充/过放)
    - 电流保护 (充电/放电过流)
    - 温度保护 (过温/低温)
    - 电芯均衡管理
    - SOC/SOH估算
    - 故障诊断和保护
    """
    
    def __init__(self, chemistry: str = "LiFePO4", cell_count: int = 16):
        """
        初始化BMS系统
        
        Args:
            chemistry: 电池化学体系
            cell_count: 电芯数量
        """
        self.chemistry = chemistry
        self.cell_count = cell_count
        
        # 根据化学体系设置保护参数
        self._set_protection_parameters()
        
        # BMS状态
        self.current_status = BMSStatus(
            timestamp=time.time(),
            system_status=BMSProtectionLevel.NORMAL,
            active_protections=[],
            charge_allowed=True,
            discharge_allowed=True,
            max_charge_current=100.0,  # 默认最大充电电流
            max_discharge_current=200.0,  # 默认最大放电电流
            balancing_active=False,
            balancing_cells=[],
            estimated_soc=0.5,
            estimated_soh=1.0
        )
        
        # 历史记录
        self.protection_history: List[BMSProtectionEvent] = []
        self.status_history: List[BMSStatus] = []
        
        # 均衡参数
        self.balancing_threshold = 0.05  # 5mV均衡阈值
        self.balancing_current = 0.1     # 100mA均衡电流
        
        logging.info(f"BMS系统初始化完成: {chemistry}, {cell_count}个电芯")
    
    def _set_protection_parameters(self) -> None:
        """根据化学体系设置保护参数"""
        if self.chemistry == "LiFePO4":
            self.protection_params = {
                # 电压保护参数 (单体电芯) - 调整为适应当前PyBaMM模型输出的阈值
                'cell_overvoltage_protection': 4.10,    # 过压保护 (适应4.007V的模型输出)
                'cell_overvoltage_warning': 4.05,       # 过压警告
                'cell_undervoltage_protection': 2.40,   # 欠压保护
                'cell_undervoltage_warning': 2.50,      # 欠压警告
                
                # 温度保护参数
                'overtemperature_protection': 60.0,     # 过温保护
                'overtemperature_warning': 55.0,        # 过温警告
                'undertemperature_protection': -20.0,   # 低温保护
                'undertemperature_warning': -10.0,      # 低温警告
                
                # 电流保护参数
                'max_charge_current': 100.0,            # 最大充电电流
                'max_discharge_current': 200.0,         # 最大放电电流
                'overcurrent_protection_time': 5.0,     # 过流保护时间
                
                # SOC保护参数
                'soc_high_protection': 0.98,            # SOC过高保护
                'soc_high_warning': 0.95,               # SOC过高警告
                'soc_low_protection': 0.02,             # SOC过低保护
                'soc_low_warning': 0.05,                # SOC过低警告
            }
        elif self.chemistry == "NMC":
            self.protection_params = {
                'cell_overvoltage_protection': 4.25,
                'cell_overvoltage_warning': 4.20,
                'cell_undervoltage_protection': 2.70,
                'cell_undervoltage_warning': 2.80,
                'overtemperature_protection': 55.0,
                'overtemperature_warning': 50.0,
                'undertemperature_protection': -20.0,
                'undertemperature_warning': -10.0,
                'max_charge_current': 80.0,
                'max_discharge_current': 150.0,
                'overcurrent_protection_time': 5.0,
                'soc_high_protection': 0.98,
                'soc_high_warning': 0.95,
                'soc_low_protection': 0.02,
                'soc_low_warning': 0.05,
            }
        else:
            # 默认保护参数
            self.protection_params = self._get_default_protection_params()
    
    def _get_default_protection_params(self) -> Dict[str, float]:
        """获取默认保护参数"""
        return {
            'cell_overvoltage_protection': 4.20,
            'cell_overvoltage_warning': 4.15,
            'cell_undervoltage_protection': 2.50,
            'cell_undervoltage_warning': 2.60,
            'overtemperature_protection': 60.0,
            'overtemperature_warning': 55.0,
            'undertemperature_protection': -20.0,
            'undertemperature_warning': -10.0,
            'max_charge_current': 100.0,
            'max_discharge_current': 200.0,
            'overcurrent_protection_time': 5.0,
            'soc_high_protection': 0.98,
            'soc_high_warning': 0.95,
            'soc_low_protection': 0.02,
            'soc_low_warning': 0.05,
        }
    
    def update_status(self, pack_state: Any, requested_current: float) -> Tuple[bool, float, BMSStatus]:
        """
        更新BMS状态并进行保护检查
        
        Args:
            pack_state: 电池包状态
            requested_current: 请求的电流 (A, 正为充电)
            
        Returns:
            (是否允许操作, 实际允许电流, BMS状态)
        """
        current_time = time.time()
        
        # 重置保护状态
        active_protections = []
        charge_allowed = True
        discharge_allowed = True
        actual_current = requested_current
        
        # 1. 电压保护检查
        voltage_protection = self._check_voltage_protection(pack_state)
        if voltage_protection:
            active_protections.extend(voltage_protection)
        
        # 2. 温度保护检查
        temperature_protection = self._check_temperature_protection(pack_state)
        if temperature_protection:
            active_protections.extend(temperature_protection)
        
        # 3. SOC保护检查
        soc_protection = self._check_soc_protection(pack_state)
        if soc_protection:
            active_protections.extend(soc_protection)
        
        # 4. 电流保护检查
        current_protection, limited_current = self._check_current_protection(
            requested_current, pack_state
        )
        if current_protection:
            active_protections.extend(current_protection)
            actual_current = limited_current
        
        # 5. 电芯均衡检查
        balancing_status = self._check_cell_balancing(pack_state)
        
        # 6. 确定系统状态和操作权限
        system_status, charge_allowed, discharge_allowed = self._determine_system_status(
            active_protections
        )
        
        # 7. 应用操作限制
        if not charge_allowed and requested_current > 0:
            actual_current = 0.0
        elif not discharge_allowed and requested_current < 0:
            actual_current = 0.0
        
        # 8. 更新BMS状态
        self.current_status = BMSStatus(
            timestamp=current_time,
            system_status=system_status,
            active_protections=active_protections,
            charge_allowed=charge_allowed,
            discharge_allowed=discharge_allowed,
            max_charge_current=self.protection_params['max_charge_current'],
            max_discharge_current=self.protection_params['max_discharge_current'],
            balancing_active=balancing_status['active'],
            balancing_cells=balancing_status['cells'],
            estimated_soc=pack_state.pack_soc,
            estimated_soh=1.0  # 简化，实际需要复杂算法
        )
        
        # 9. 记录保护事件
        for protection in active_protections:
            if protection.protection_level in [BMSProtectionLevel.PROTECTION, BMSProtectionLevel.FAULT]:
                self.protection_history.append(protection)
                logging.debug(f"BMS保护触发: {protection.description}")  # 改为debug级别
        
        # 10. 记录状态历史
        self.status_history.append(self.current_status)
        
        # 限制历史记录长度
        if len(self.status_history) > 1000:
            self.status_history = self.status_history[-500:]

        # 修复BMS操作允许逻辑
        # 0.0A电流（开路状态）应该始终被允许，这是正常状态
        if abs(requested_current) < 0.01:  # 0电流或接近0电流
            operation_allowed = True  # 开路状态总是允许的
        else:
            # 非零电流需要检查充放电权限
            if requested_current > 0:  # 充电请求
                operation_allowed = charge_allowed
            else:  # 放电请求
                operation_allowed = discharge_allowed

        return operation_allowed, actual_current, self.current_status

    def _check_voltage_protection(self, pack_state: Any) -> List[BMSProtectionEvent]:
        """检查电压保护"""
        protections = []
        current_time = time.time()

        # 检查每个电芯的电压
        for i, cell_state in enumerate(pack_state.cell_states):
            voltage = cell_state.voltage

            # 过压检查
            if voltage >= self.protection_params['cell_overvoltage_protection']:
                protections.append(BMSProtectionEvent(
                    timestamp=current_time,
                    protection_type=BMSProtectionType.OVERVOLTAGE,
                    protection_level=BMSProtectionLevel.PROTECTION,
                    affected_cells=[i],
                    values={'voltage': voltage, 'limit': self.protection_params['cell_overvoltage_protection']},
                    description=f"电芯{i}过压保护: {voltage:.3f}V >= {self.protection_params['cell_overvoltage_protection']:.3f}V",
                    action_taken="禁止充电"
                ))
            elif voltage >= self.protection_params['cell_overvoltage_warning']:
                protections.append(BMSProtectionEvent(
                    timestamp=current_time,
                    protection_type=BMSProtectionType.OVERVOLTAGE,
                    protection_level=BMSProtectionLevel.WARNING,
                    affected_cells=[i],
                    values={'voltage': voltage, 'limit': self.protection_params['cell_overvoltage_warning']},
                    description=f"电芯{i}过压警告: {voltage:.3f}V >= {self.protection_params['cell_overvoltage_warning']:.3f}V",
                    action_taken="限制充电电流"
                ))

            # 欠压检查
            if voltage <= self.protection_params['cell_undervoltage_protection']:
                protections.append(BMSProtectionEvent(
                    timestamp=current_time,
                    protection_type=BMSProtectionType.UNDERVOLTAGE,
                    protection_level=BMSProtectionLevel.PROTECTION,
                    affected_cells=[i],
                    values={'voltage': voltage, 'limit': self.protection_params['cell_undervoltage_protection']},
                    description=f"电芯{i}欠压保护: {voltage:.3f}V <= {self.protection_params['cell_undervoltage_protection']:.3f}V",
                    action_taken="禁止放电"
                ))
            elif voltage <= self.protection_params['cell_undervoltage_warning']:
                protections.append(BMSProtectionEvent(
                    timestamp=current_time,
                    protection_type=BMSProtectionType.UNDERVOLTAGE,
                    protection_level=BMSProtectionLevel.WARNING,
                    affected_cells=[i],
                    values={'voltage': voltage, 'limit': self.protection_params['cell_undervoltage_warning']},
                    description=f"电芯{i}欠压警告: {voltage:.3f}V <= {self.protection_params['cell_undervoltage_warning']:.3f}V",
                    action_taken="限制放电电流"
                ))

        return protections

    def _check_temperature_protection(self, pack_state: Any) -> List[BMSProtectionEvent]:
        """检查温度保护"""
        protections = []
        current_time = time.time()

        # 检查每个电芯的温度
        for i, cell_state in enumerate(pack_state.cell_states):
            temperature = cell_state.temperature

            # 过温检查
            if temperature >= self.protection_params['overtemperature_protection']:
                protections.append(BMSProtectionEvent(
                    timestamp=current_time,
                    protection_type=BMSProtectionType.OVERTEMPERATURE,
                    protection_level=BMSProtectionLevel.PROTECTION,
                    affected_cells=[i],
                    values={'temperature': temperature, 'limit': self.protection_params['overtemperature_protection']},
                    description=f"电芯{i}过温保护: {temperature:.1f}°C >= {self.protection_params['overtemperature_protection']:.1f}°C",
                    action_taken="禁止充放电"
                ))
            elif temperature >= self.protection_params['overtemperature_warning']:
                protections.append(BMSProtectionEvent(
                    timestamp=current_time,
                    protection_type=BMSProtectionType.OVERTEMPERATURE,
                    protection_level=BMSProtectionLevel.WARNING,
                    affected_cells=[i],
                    values={'temperature': temperature, 'limit': self.protection_params['overtemperature_warning']},
                    description=f"电芯{i}过温警告: {temperature:.1f}°C >= {self.protection_params['overtemperature_warning']:.1f}°C",
                    action_taken="限制充放电电流"
                ))

            # 低温检查
            if temperature <= self.protection_params['undertemperature_protection']:
                protections.append(BMSProtectionEvent(
                    timestamp=current_time,
                    protection_type=BMSProtectionType.UNDERTEMPERATURE,
                    protection_level=BMSProtectionLevel.PROTECTION,
                    affected_cells=[i],
                    values={'temperature': temperature, 'limit': self.protection_params['undertemperature_protection']},
                    description=f"电芯{i}低温保护: {temperature:.1f}°C <= {self.protection_params['undertemperature_protection']:.1f}°C",
                    action_taken="禁止充电"
                ))
            elif temperature <= self.protection_params['undertemperature_warning']:
                protections.append(BMSProtectionEvent(
                    timestamp=current_time,
                    protection_type=BMSProtectionType.UNDERTEMPERATURE,
                    protection_level=BMSProtectionLevel.WARNING,
                    affected_cells=[i],
                    values={'temperature': temperature, 'limit': self.protection_params['undertemperature_warning']},
                    description=f"电芯{i}低温警告: {temperature:.1f}°C <= {self.protection_params['undertemperature_warning']:.1f}°C",
                    action_taken="限制充电电流"
                ))

        return protections

    def _check_soc_protection(self, pack_state: Any) -> List[BMSProtectionEvent]:
        """检查SOC保护"""
        protections = []
        current_time = time.time()

        # 检查包级SOC
        pack_soc = pack_state.pack_soc

        # SOC过高检查
        if pack_soc >= self.protection_params['soc_high_protection']:
            protections.append(BMSProtectionEvent(
                timestamp=current_time,
                protection_type=BMSProtectionType.SOC_HIGH,
                protection_level=BMSProtectionLevel.PROTECTION,
                affected_cells=list(range(self.cell_count)),
                values={'soc': pack_soc, 'limit': self.protection_params['soc_high_protection']},
                description=f"包SOC过高保护: {pack_soc:.3f} >= {self.protection_params['soc_high_protection']:.3f}",
                action_taken="禁止充电"
            ))
        elif pack_soc >= self.protection_params['soc_high_warning']:
            protections.append(BMSProtectionEvent(
                timestamp=current_time,
                protection_type=BMSProtectionType.SOC_HIGH,
                protection_level=BMSProtectionLevel.WARNING,
                affected_cells=list(range(self.cell_count)),
                values={'soc': pack_soc, 'limit': self.protection_params['soc_high_warning']},
                description=f"包SOC过高警告: {pack_soc:.3f} >= {self.protection_params['soc_high_warning']:.3f}",
                action_taken="限制充电电流"
            ))

        # SOC过低检查
        if pack_soc <= self.protection_params['soc_low_protection']:
            protections.append(BMSProtectionEvent(
                timestamp=current_time,
                protection_type=BMSProtectionType.SOC_LOW,
                protection_level=BMSProtectionLevel.PROTECTION,
                affected_cells=list(range(self.cell_count)),
                values={'soc': pack_soc, 'limit': self.protection_params['soc_low_protection']},
                description=f"包SOC过低保护: {pack_soc:.3f} <= {self.protection_params['soc_low_protection']:.3f}",
                action_taken="禁止放电"
            ))
        elif pack_soc <= self.protection_params['soc_low_warning']:
            protections.append(BMSProtectionEvent(
                timestamp=current_time,
                protection_type=BMSProtectionType.SOC_LOW,
                protection_level=BMSProtectionLevel.WARNING,
                affected_cells=list(range(self.cell_count)),
                values={'soc': pack_soc, 'limit': self.protection_params['soc_low_warning']},
                description=f"包SOC过低警告: {pack_soc:.3f} <= {self.protection_params['soc_low_warning']:.3f}",
                action_taken="限制放电电流"
            ))

        # 检查单体电芯SOC不均衡
        cell_socs = [cell.soc for cell in pack_state.cell_states]
        soc_max = max(cell_socs)
        soc_min = min(cell_socs)
        soc_diff = soc_max - soc_min

        if soc_diff > 0.1:  # SOC差异超过10%
            max_cells = [i for i, soc in enumerate(cell_socs) if soc == soc_max]
            min_cells = [i for i, soc in enumerate(cell_socs) if soc == soc_min]

            protections.append(BMSProtectionEvent(
                timestamp=current_time,
                protection_type=BMSProtectionType.CELL_IMBALANCE,
                protection_level=BMSProtectionLevel.WARNING,
                affected_cells=max_cells + min_cells,
                values={'soc_diff': soc_diff, 'soc_max': soc_max, 'soc_min': soc_min},
                description=f"电芯SOC不均衡: 最大{soc_max:.3f} - 最小{soc_min:.3f} = {soc_diff:.3f}",
                action_taken="启动均衡"
            ))

        return protections

    def _check_current_protection(self, requested_current: float, pack_state: Any) -> Tuple[List[BMSProtectionEvent], float]:
        """检查电流保护并返回限制后的电流"""
        protections = []
        current_time = time.time()
        limited_current = requested_current

        # 充电电流检查
        if requested_current > 0:  # 充电
            max_allowed = self.protection_params['max_charge_current']

            # 根据温度调整最大充电电流
            avg_temp = pack_state.pack_temperature
            if avg_temp > 45.0:  # 高温降额
                temp_factor = max(0.5, (55.0 - avg_temp) / 10.0)
                max_allowed *= temp_factor
            elif avg_temp < 0.0:  # 低温降额
                temp_factor = max(0.1, (avg_temp + 20.0) / 20.0)
                max_allowed *= temp_factor

            # 根据SOC调整最大充电电流
            if pack_state.pack_soc > 0.9:  # 高SOC降额
                soc_factor = max(0.3, (1.0 - pack_state.pack_soc) / 0.1)
                max_allowed *= soc_factor

            if requested_current > max_allowed:
                limited_current = max_allowed

                if requested_current > self.protection_params['max_charge_current']:
                    protections.append(BMSProtectionEvent(
                        timestamp=current_time,
                        protection_type=BMSProtectionType.OVERCURRENT_CHARGE,
                        protection_level=BMSProtectionLevel.PROTECTION,
                        affected_cells=list(range(self.cell_count)),
                        values={'requested': requested_current, 'limit': self.protection_params['max_charge_current']},
                        description=f"充电过流保护: {requested_current:.1f}A > {self.protection_params['max_charge_current']:.1f}A",
                        action_taken=f"限制充电电流至{limited_current:.1f}A"
                    ))
                else:
                    protections.append(BMSProtectionEvent(
                        timestamp=current_time,
                        protection_type=BMSProtectionType.OVERCURRENT_CHARGE,
                        protection_level=BMSProtectionLevel.WARNING,
                        affected_cells=list(range(self.cell_count)),
                        values={'requested': requested_current, 'limited': limited_current},
                        description=f"充电电流降额: 温度{avg_temp:.1f}°C, SOC{pack_state.pack_soc:.3f}",
                        action_taken=f"限制充电电流至{limited_current:.1f}A"
                    ))

        # 放电电流检查
        elif requested_current < 0:  # 放电
            max_allowed = self.protection_params['max_discharge_current']

            # 根据温度调整最大放电电流
            avg_temp = pack_state.pack_temperature
            if avg_temp > 50.0:  # 高温降额
                temp_factor = max(0.6, (60.0 - avg_temp) / 10.0)
                max_allowed *= temp_factor
            elif avg_temp < -10.0:  # 低温降额
                temp_factor = max(0.2, (avg_temp + 20.0) / 10.0)
                max_allowed *= temp_factor

            # 根据SOC调整最大放电电流
            if pack_state.pack_soc < 0.1:  # 低SOC降额
                soc_factor = max(0.3, pack_state.pack_soc / 0.1)
                max_allowed *= soc_factor

            if abs(requested_current) > max_allowed:
                limited_current = -max_allowed

                if abs(requested_current) > self.protection_params['max_discharge_current']:
                    protections.append(BMSProtectionEvent(
                        timestamp=current_time,
                        protection_type=BMSProtectionType.OVERCURRENT_DISCHARGE,
                        protection_level=BMSProtectionLevel.PROTECTION,
                        affected_cells=list(range(self.cell_count)),
                        values={'requested': abs(requested_current), 'limit': self.protection_params['max_discharge_current']},
                        description=f"放电过流保护: {abs(requested_current):.1f}A > {self.protection_params['max_discharge_current']:.1f}A",
                        action_taken=f"限制放电电流至{max_allowed:.1f}A"
                    ))
                else:
                    protections.append(BMSProtectionEvent(
                        timestamp=current_time,
                        protection_type=BMSProtectionType.OVERCURRENT_DISCHARGE,
                        protection_level=BMSProtectionLevel.WARNING,
                        affected_cells=list(range(self.cell_count)),
                        values={'requested': abs(requested_current), 'limited': max_allowed},
                        description=f"放电电流降额: 温度{avg_temp:.1f}°C, SOC{pack_state.pack_soc:.3f}",
                        action_taken=f"限制放电电流至{max_allowed:.1f}A"
                    ))

        return protections, limited_current

    def _check_cell_balancing(self, pack_state: Any) -> Dict[str, Any]:
        """检查电芯均衡状态"""
        cell_voltages = [cell.voltage for cell in pack_state.cell_states]
        voltage_max = max(cell_voltages)
        voltage_min = min(cell_voltages)
        voltage_diff = voltage_max - voltage_min

        balancing_active = False
        balancing_cells = []

        # 如果电压差异超过阈值，启动均衡
        if voltage_diff > self.balancing_threshold:
            balancing_active = True
            # 找出需要均衡的高电压电芯
            voltage_threshold = voltage_min + voltage_diff * 0.8  # 均衡高于80%分位的电芯
            balancing_cells = [
                i for i, voltage in enumerate(cell_voltages)
                if voltage > voltage_threshold
            ]

            logging.info(f"启动电芯均衡: 电压差{voltage_diff*1000:.1f}mV, 均衡电芯{balancing_cells}")

        return {
            'active': balancing_active,
            'cells': balancing_cells,
            'voltage_diff': voltage_diff,
            'voltage_max': voltage_max,
            'voltage_min': voltage_min
        }

    def _determine_system_status(self, active_protections: List[BMSProtectionEvent]) -> Tuple[BMSProtectionLevel, bool, bool]:
        """确定系统状态和操作权限"""
        if not active_protections:
            return BMSProtectionLevel.NORMAL, True, True

        # 检查是否有故障级保护
        has_fault = any(p.protection_level == BMSProtectionLevel.FAULT for p in active_protections)
        if has_fault:
            return BMSProtectionLevel.FAULT, False, False

        # 检查是否有保护级事件
        has_protection = any(p.protection_level == BMSProtectionLevel.PROTECTION for p in active_protections)
        if has_protection:
            # 根据保护类型确定操作权限
            charge_allowed = True
            discharge_allowed = True

            for protection in active_protections:
                if protection.protection_level == BMSProtectionLevel.PROTECTION:
                    if protection.protection_type in [
                        BMSProtectionType.OVERVOLTAGE,
                        BMSProtectionType.SOC_HIGH,
                        BMSProtectionType.OVERCURRENT_CHARGE,
                        BMSProtectionType.OVERTEMPERATURE,
                        BMSProtectionType.UNDERTEMPERATURE
                    ]:
                        charge_allowed = False

                    if protection.protection_type in [
                        BMSProtectionType.UNDERVOLTAGE,
                        BMSProtectionType.SOC_LOW,
                        BMSProtectionType.OVERCURRENT_DISCHARGE,
                        BMSProtectionType.OVERTEMPERATURE
                    ]:
                        discharge_allowed = False

            return BMSProtectionLevel.PROTECTION, charge_allowed, discharge_allowed

        # 只有警告级事件
        return BMSProtectionLevel.WARNING, True, True

    def get_system_info(self) -> Dict[str, Any]:
        """获取BMS系统信息"""
        return {
            'chemistry': self.chemistry,
            'cell_count': self.cell_count,
            'protection_parameters': self.protection_params,
            'current_status': {
                'system_status': self.current_status.system_status.value,
                'charge_allowed': self.current_status.charge_allowed,
                'discharge_allowed': self.current_status.discharge_allowed,
                'max_charge_current': self.current_status.max_charge_current,
                'max_discharge_current': self.current_status.max_discharge_current,
                'balancing_active': self.current_status.balancing_active,
                'balancing_cells': self.current_status.balancing_cells,
                'estimated_soc': self.current_status.estimated_soc,
                'estimated_soh': self.current_status.estimated_soh,
                'active_protections_count': len(self.current_status.active_protections)
            },
            'protection_history_count': len(self.protection_history),
            'balancing_threshold_mv': self.balancing_threshold * 1000
        }

    def get_protection_summary(self) -> Dict[str, Any]:
        """获取保护事件摘要"""
        if not self.current_status.active_protections:
            return {'status': 'normal', 'protections': []}

        protection_summary = []
        for protection in self.current_status.active_protections:
            protection_summary.append({
                'type': protection.protection_type.value,
                'level': protection.protection_level.value,
                'affected_cells': protection.affected_cells,
                'description': protection.description,
                'action': protection.action_taken
            })

        return {
            'status': self.current_status.system_status.value,
            'protections': protection_summary,
            'charge_allowed': self.current_status.charge_allowed,
            'discharge_allowed': self.current_status.discharge_allowed
        }

    def reset_protections(self) -> None:
        """重置保护状态 (仅用于测试)"""
        self.current_status.active_protections = []
        self.current_status.system_status = BMSProtectionLevel.NORMAL
        self.current_status.charge_allowed = True
        self.current_status.discharge_allowed = True
        logging.info("BMS保护状态已重置")

    def simulate_balancing_effect(self, pack_state: Any, dt: float) -> Any:
        """模拟均衡效果 (简化模型)"""
        if not self.current_status.balancing_active:
            return pack_state

        # 简化的均衡模拟：降低高电压电芯的电压
        balancing_effect = self.balancing_current * dt / 3600.0  # Ah
        voltage_reduction = balancing_effect * 0.01  # 简化的电压降低

        for cell_id in self.current_status.balancing_cells:
            if cell_id < len(pack_state.cell_states):
                # 简单地降低电压 (实际应该通过SOC变化)
                pack_state.cell_states[cell_id].voltage -= voltage_reduction
                pack_state.cell_states[cell_id].soc -= balancing_effect / 2.3  # 假设2.3Ah容量
                pack_state.cell_states[cell_id].soc = max(0.0, pack_state.cell_states[cell_id].soc)

        # 重新计算包级参数
        pack_state.pack_voltage = sum(cell.voltage for cell in pack_state.cell_states)
        pack_state.pack_soc = sum(cell.soc for cell in pack_state.cell_states) / len(pack_state.cell_states)

        return pack_state

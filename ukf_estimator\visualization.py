"""
可视化模块

提供UKF估算结果的可视化分析功能。
支持实时绘图、历史数据分析和性能评估图表。
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import List, Dict, Optional, Tuple
import logging

from .state_estimator import EstimationResult
from .data_interface import MeasurementData
from .validation import ValidationMetrics


class EstimationVisualizer:
    """
    估算可视化器
    
    提供多种可视化功能：
    1. 实时估算结果显示
    2. 历史数据对比分析
    3. 误差分析图表
    4. 参数收敛曲线
    5. 性能评估报告
    """
    
    def __init__(self, figsize: Tuple[int, int] = (12, 8)):
        """
        初始化可视化器
        
        Args:
            figsize: 图形大小
        """
        self.figsize = figsize
        plt.style.use('default')  # 使用默认样式
        logging.info("估算可视化器初始化完成")
    
    def plot_estimation_results(self, estimation_results: List[EstimationResult],
                               ground_truth_data: Optional[List[MeasurementData]] = None,
                               save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制估算结果对比图
        
        Args:
            estimation_results: UKF估算结果
            ground_truth_data: 真实值数据 (可选)
            save_path: 保存路径 (可选)
            
        Returns:
            matplotlib图形对象
        """
        if not estimation_results:
            raise ValueError("估算结果为空")
        
        # 提取数据
        timestamps = [r.timestamp for r in estimation_results]
        soc_est = [r.soc for r in estimation_results]
        r0_est = [r.r0 for r in estimation_results]
        r1_est = [r.r1 for r in estimation_results]
        voltage_pred = [r.predicted_voltage for r in estimation_results]
        voltage_meas = [r.measured_voltage for r in estimation_results]
        
        # 转换时间戳为相对时间
        if timestamps:
            start_time = timestamps[0]
            time_relative = [(t - start_time) / 60.0 for t in timestamps]  # 转换为分钟
        else:
            time_relative = []
        
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=self.figsize)
        fig.suptitle('UKF电池状态估算结果', fontsize=16, fontweight='bold')
        
        # 1. SOC估算
        ax1 = axes[0, 0]
        ax1.plot(time_relative, soc_est, 'b-', linewidth=2, label='UKF估算')
        
        if ground_truth_data:
            # 对齐真实数据
            truth_dict = {d.timestamp: d for d in ground_truth_data if d.soc_true is not None}
            soc_true = []
            time_true = []
            for t in timestamps:
                closest_time = min(truth_dict.keys(), key=lambda x: abs(x - t), default=None)
                if closest_time and abs(closest_time - t) < 1.0:  # 1秒容差
                    soc_true.append(truth_dict[closest_time].soc_true)
                    time_true.append((t - start_time) / 60.0)
            
            if soc_true:
                ax1.plot(time_true, soc_true, 'r--', linewidth=2, label='真实值')
        
        ax1.set_xlabel('时间 (分钟)')
        ax1.set_ylabel('SOC')
        ax1.set_title('SOC估算对比')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 内阻估算
        ax2 = axes[0, 1]
        ax2.plot(time_relative, [r*1000 for r in r0_est], 'g-', linewidth=2, label='欧姆内阻 R₀')
        ax2.plot(time_relative, [r*1000 for r in r1_est], 'orange', linewidth=2, label='极化内阻 R₁')
        
        if ground_truth_data:
            r0_true = []
            time_r0_true = []
            for t in timestamps:
                closest_time = min(truth_dict.keys(), key=lambda x: abs(x - t), default=None)
                if closest_time and abs(closest_time - t) < 1.0:
                    truth_data = truth_dict[closest_time]
                    if truth_data.r0_true is not None:
                        r0_true.append(truth_data.r0_true * 1000)
                        time_r0_true.append((t - start_time) / 60.0)
            
            if r0_true:
                ax2.plot(time_r0_true, r0_true, 'r--', linewidth=2, label='真实R₀')
        
        ax2.set_xlabel('时间 (分钟)')
        ax2.set_ylabel('内阻 (mΩ)')
        ax2.set_title('内阻估算')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 电压对比
        ax3 = axes[1, 0]
        ax3.plot(time_relative, voltage_pred, 'b-', linewidth=2, label='预测电压')
        ax3.plot(time_relative, voltage_meas, 'r-', linewidth=1, alpha=0.7, label='测量电压')
        ax3.set_xlabel('时间 (分钟)')
        ax3.set_ylabel('电压 (V)')
        ax3.set_title('电压预测对比')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 电压误差
        ax4 = axes[1, 1]
        voltage_errors = [abs(p - m) * 1000 for p, m in zip(voltage_pred, voltage_meas)]
        ax4.plot(time_relative, voltage_errors, 'purple', linewidth=2)
        ax4.set_xlabel('时间 (分钟)')
        ax4.set_ylabel('电压误差 (mV)')
        ax4.set_title('电压预测误差')
        ax4.grid(True, alpha=0.3)
        
        # 添加统计信息
        if voltage_errors:
            mean_error = np.mean(voltage_errors)
            max_error = np.max(voltage_errors)
            ax4.axhline(y=mean_error, color='red', linestyle='--', alpha=0.7, 
                       label=f'平均误差: {mean_error:.1f}mV')
            ax4.text(0.02, 0.98, f'最大误差: {max_error:.1f}mV', 
                    transform=ax4.transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logging.info(f"估算结果图已保存: {save_path}")
        
        return fig
    
    def plot_parameter_convergence(self, estimation_results: List[EstimationResult],
                                  save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制参数收敛曲线
        
        Args:
            estimation_results: 估算结果
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        if not estimation_results:
            raise ValueError("估算结果为空")
        
        # 提取数据
        timestamps = [r.timestamp for r in estimation_results]
        r0_values = [r.r0 * 1000 for r in estimation_results]  # 转换为mΩ
        r1_values = [r.r1 * 1000 for r in estimation_results]
        c1_values = [r.c1 for r in estimation_results]
        r0_uncertainties = [r.r0_uncertainty * 1000 for r in estimation_results]
        
        # 转换时间
        start_time = timestamps[0] if timestamps else 0
        time_relative = [(t - start_time) / 60.0 for t in timestamps]
        
        # 创建图形
        fig, axes = plt.subplots(2, 2, figsize=self.figsize)
        fig.suptitle('UKF参数收敛分析', fontsize=16, fontweight='bold')
        
        # 1. R0收敛
        ax1 = axes[0, 0]
        ax1.plot(time_relative, r0_values, 'b-', linewidth=2, label='R₀估算值')
        if r0_uncertainties and any(u > 0 for u in r0_uncertainties):
            r0_upper = [r + u for r, u in zip(r0_values, r0_uncertainties)]
            r0_lower = [r - u for r, u in zip(r0_values, r0_uncertainties)]
            ax1.fill_between(time_relative, r0_lower, r0_upper, alpha=0.3, label='不确定性区间')
        ax1.set_xlabel('时间 (分钟)')
        ax1.set_ylabel('欧姆内阻 (mΩ)')
        ax1.set_title('欧姆内阻收敛')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. R1收敛
        ax2 = axes[0, 1]
        ax2.plot(time_relative, r1_values, 'g-', linewidth=2)
        ax2.set_xlabel('时间 (分钟)')
        ax2.set_ylabel('极化内阻 (mΩ)')
        ax2.set_title('极化内阻收敛')
        ax2.grid(True, alpha=0.3)
        
        # 3. C1收敛
        ax3 = axes[1, 0]
        ax3.plot(time_relative, c1_values, 'orange', linewidth=2)
        ax3.set_xlabel('时间 (分钟)')
        ax3.set_ylabel('极化电容 (F)')
        ax3.set_title('极化电容收敛')
        ax3.grid(True, alpha=0.3)
        
        # 4. 参数变化率
        ax4 = axes[1, 1]
        if len(r0_values) > 1:
            r0_changes = [abs(r0_values[i] - r0_values[i-1]) for i in range(1, len(r0_values))]
            time_changes = time_relative[1:]
            ax4.semilogy(time_changes, r0_changes, 'purple', linewidth=2)
            ax4.set_xlabel('时间 (分钟)')
            ax4.set_ylabel('R₀变化率 (mΩ)')
            ax4.set_title('参数收敛速度')
            ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logging.info(f"参数收敛图已保存: {save_path}")
        
        return fig
    
    def plot_validation_metrics(self, metrics: ValidationMetrics,
                               save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制验证指标图表
        
        Args:
            metrics: 验证指标
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        fig, axes = plt.subplots(2, 2, figsize=self.figsize)
        fig.suptitle('UKF估算性能验证', fontsize=16, fontweight='bold')
        
        # 1. 误差对比柱状图
        ax1 = axes[0, 0]
        categories = ['SOC\nMAE', 'SOC\nRMSE', 'R₀\nMAE\n(mΩ)', 'R₀\nRMSE\n(mΩ)']
        values = [metrics.soc_mae, metrics.soc_rmse, 
                 metrics.r0_mae*1000, metrics.r0_rmse*1000]
        colors = ['skyblue', 'lightcoral', 'lightgreen', 'gold']
        
        bars = ax1.bar(categories, values, color=colors, alpha=0.7)
        ax1.set_ylabel('误差值')
        ax1.set_title('估算误差对比')
        
        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height,
                    f'{value:.4f}', ha='center', va='bottom')
        
        # 2. 相关系数
        ax2 = axes[0, 1]
        corr_categories = ['SOC相关系数', 'R₀相关系数']
        corr_values = [metrics.soc_correlation, metrics.r0_correlation]
        bars2 = ax2.bar(corr_categories, corr_values, color=['blue', 'green'], alpha=0.7)
        ax2.set_ylabel('相关系数')
        ax2.set_title('估算相关性')
        ax2.set_ylim(0, 1)
        
        for bar, value in zip(bars2, corr_values):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'{value:.3f}', ha='center', va='bottom')
        
        # 3. 性能指标饼图
        ax3 = axes[1, 0]
        success_rate = metrics.estimation_success_rate
        failure_rate = 1 - success_rate
        labels = ['成功估算', '失败估算']
        sizes = [success_rate, failure_rate]
        colors = ['lightgreen', 'lightcoral']
        
        ax3.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        ax3.set_title('估算成功率')
        
        # 4. 数据统计
        ax4 = axes[1, 1]
        ax4.axis('off')  # 关闭坐标轴
        
        # 创建文本统计信息
        stats_text = f"""
数据统计:
• 总数据点: {metrics.total_points}
• 有效数据点: {metrics.valid_points}
• 成功率: {metrics.estimation_success_rate:.1%}

电压预测:
• MAE: {metrics.voltage_mae:.3f} V
• RMSE: {metrics.voltage_rmse:.3f} V
• 最大误差: {metrics.voltage_max_error:.3f} V

系统性能:
• 收敛时间: {metrics.convergence_time:.1f} s
• 计算效率: {metrics.computational_efficiency:.2f}
        """
        
        ax4.text(0.1, 0.9, stats_text, transform=ax4.transAxes, fontsize=10,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logging.info(f"验证指标图已保存: {save_path}")
        
        return fig
    
    def create_real_time_plot(self) -> Tuple[plt.Figure, Dict]:
        """
        创建实时绘图窗口
        
        Returns:
            (图形对象, 绘图元素字典)
        """
        fig, axes = plt.subplots(2, 2, figsize=self.figsize)
        fig.suptitle('UKF实时估算监控', fontsize=16, fontweight='bold')
        
        # 初始化空图
        plot_elements = {
            'soc_line': axes[0, 0].plot([], [], 'b-', linewidth=2)[0],
            'r0_line': axes[0, 1].plot([], [], 'g-', linewidth=2)[0],
            'voltage_pred_line': axes[1, 0].plot([], [], 'b-', linewidth=2)[0],
            'voltage_meas_line': axes[1, 0].plot([], [], 'r-', linewidth=1, alpha=0.7)[0],
            'error_line': axes[1, 1].plot([], [], 'purple', linewidth=2)[0]
        }
        
        # 设置坐标轴
        axes[0, 0].set_xlabel('时间 (s)')
        axes[0, 0].set_ylabel('SOC')
        axes[0, 0].set_title('SOC估算')
        axes[0, 0].grid(True, alpha=0.3)
        
        axes[0, 1].set_xlabel('时间 (s)')
        axes[0, 1].set_ylabel('内阻 (mΩ)')
        axes[0, 1].set_title('内阻估算')
        axes[0, 1].grid(True, alpha=0.3)
        
        axes[1, 0].set_xlabel('时间 (s)')
        axes[1, 0].set_ylabel('电压 (V)')
        axes[1, 0].set_title('电压预测')
        axes[1, 0].legend(['预测', '测量'])
        axes[1, 0].grid(True, alpha=0.3)
        
        axes[1, 1].set_xlabel('时间 (s)')
        axes[1, 1].set_ylabel('电压误差 (mV)')
        axes[1, 1].set_title('预测误差')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.ion()  # 开启交互模式
        
        return fig, plot_elements

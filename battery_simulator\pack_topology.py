"""
电池包拓扑结构定义

定义电池包的串并联结构、物理布局、热连接和电气连接，
为电池包级别的PyBaMM仿真提供基础架构。
"""

import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, field
from enum import Enum


class CellConnectionType(Enum):
    """电芯连接类型"""
    SERIES = "series"           # 串联
    PARALLEL = "parallel"       # 并联
    ISOLATED = "isolated"       # 隔离


@dataclass
class CellPosition:
    """电芯物理位置"""
    x: float                    # X坐标 (m)
    y: float                    # Y坐标 (m)
    z: float                    # Z坐标 (m)
    row: int                    # 行号
    col: int                    # 列号


@dataclass
class ThermalConnection:
    """热连接定义"""
    cell1_id: int              # 电芯1 ID
    cell2_id: int              # 电芯2 ID
    thermal_resistance: float  # 热阻 (K/W)
    contact_area: float        # 接触面积 (m²)


@dataclass
class ElectricalConnection:
    """电气连接定义"""
    cell1_id: int              # 电芯1 ID
    cell2_id: int              # 电芯2 ID
    connection_type: CellConnectionType  # 连接类型
    resistance: float          # 连接电阻 (Ω)
    current_capacity: float    # 载流能力 (A)


class PackTopology:
    """
    电池包拓扑结构
    
    定义电池包的完整拓扑信息，包括：
    - 串并联配置
    - 电芯物理位置
    - 热连接矩阵
    - 电气连接图
    - 热管理系统接口
    """
    
    def __init__(self, series_count: int, parallel_count: int,
                 pack_name: str = "Generic Pack"):
        """
        初始化电池包拓扑

        Args:
            series_count: 串联电芯数量
            parallel_count: 并联支路数量
            pack_name: 电池包名称

        Raises:
            ValueError: 当参数无效时抛出异常
        """
        # 参数验证 - 修复Bug: 异常输入应该抛出异常
        if not isinstance(series_count, int):
            raise ValueError(f"串联数量必须是整数，得到: {type(series_count).__name__}")
        if not isinstance(parallel_count, int):
            raise ValueError(f"并联数量必须是整数，得到: {type(parallel_count).__name__}")

        if series_count <= 0:
            raise ValueError(f"串联数量必须大于0，得到: {series_count}")
        if parallel_count <= 0:
            raise ValueError(f"并联数量必须大于0，得到: {parallel_count}")

        # 合理性检查
        if series_count > 1000:
            raise ValueError(f"串联数量过大，最大支持1000，得到: {series_count}")
        if parallel_count > 1000:
            raise ValueError(f"并联数量过大，最大支持1000，得到: {parallel_count}")

        total_cells = series_count * parallel_count
        if total_cells > 10000:
            raise ValueError(f"总电芯数量过大，最大支持10000，得到: {total_cells}")

        self.series_count = series_count
        self.parallel_count = parallel_count
        self.total_cells = total_cells
        self.pack_name = pack_name
        
        # 拓扑数据
        self.cell_positions: Dict[int, CellPosition] = {}
        self.thermal_connections: List[ThermalConnection] = []
        self.electrical_connections: List[ElectricalConnection] = []
        
        # 连接矩阵
        self.thermal_resistance_matrix: Optional[np.ndarray] = None
        self.electrical_adjacency_matrix: Optional[np.ndarray] = None
        
        # 包级参数
        self.pack_dimensions = {'length': 0.0, 'width': 0.0, 'height': 0.0}
        self.cooling_system = {'type': 'natural', 'capacity': 0.0}
        
        logging.info(f"电池包拓扑初始化: {pack_name} ({series_count}S{parallel_count}P, {self.total_cells}个电芯)")
    
    def set_cell_position(self, cell_id: int, x: float, y: float, z: float, 
                         row: int, col: int) -> None:
        """设置电芯物理位置"""
        if cell_id >= self.total_cells:
            raise ValueError(f"电芯ID {cell_id} 超出范围 (0-{self.total_cells-1})")
        
        self.cell_positions[cell_id] = CellPosition(x, y, z, row, col)
    
    def add_thermal_connection(self, cell1_id: int, cell2_id: int, 
                             thermal_resistance: float, contact_area: float = 0.001) -> None:
        """添加热连接"""
        if cell1_id >= self.total_cells or cell2_id >= self.total_cells:
            raise ValueError("电芯ID超出范围")
        
        connection = ThermalConnection(
            cell1_id=cell1_id,
            cell2_id=cell2_id,
            thermal_resistance=thermal_resistance,
            contact_area=contact_area
        )
        self.thermal_connections.append(connection)
    
    def add_electrical_connection(self, cell1_id: int, cell2_id: int, 
                                connection_type: CellConnectionType,
                                resistance: float = 0.001, 
                                current_capacity: float = 200.0) -> None:
        """添加电气连接"""
        if cell1_id >= self.total_cells or cell2_id >= self.total_cells:
            raise ValueError("电芯ID超出范围")
        
        connection = ElectricalConnection(
            cell1_id=cell1_id,
            cell2_id=cell2_id,
            connection_type=connection_type,
            resistance=resistance,
            current_capacity=current_capacity
        )
        self.electrical_connections.append(connection)
    
    def build_thermal_matrix(self) -> np.ndarray:
        """构建热阻矩阵"""
        # 初始化为无穷大热阻 (无连接)
        matrix = np.full((self.total_cells, self.total_cells), np.inf)
        
        # 对角线为0 (自身)
        np.fill_diagonal(matrix, 0.0)
        
        # 填入热连接
        for conn in self.thermal_connections:
            matrix[conn.cell1_id, conn.cell2_id] = conn.thermal_resistance
            matrix[conn.cell2_id, conn.cell1_id] = conn.thermal_resistance  # 对称
        
        self.thermal_resistance_matrix = matrix
        return matrix
    
    def build_electrical_matrix(self) -> np.ndarray:
        """构建电气连接邻接矩阵"""
        matrix = np.zeros((self.total_cells, self.total_cells))
        
        for conn in self.electrical_connections:
            # 用连接电阻的倒数表示连接强度
            conductance = 1.0 / conn.resistance if conn.resistance > 0 else 1e6
            matrix[conn.cell1_id, conn.cell2_id] = conductance
            matrix[conn.cell2_id, conn.cell1_id] = conductance
        
        self.electrical_adjacency_matrix = matrix
        return matrix
    
    def get_series_groups(self) -> List[List[int]]:
        """获取串联组"""
        groups = []
        for parallel_branch in range(self.parallel_count):
            group = []
            for series_pos in range(self.series_count):
                cell_id = parallel_branch * self.series_count + series_pos
                group.append(cell_id)
            groups.append(group)
        return groups
    
    def get_parallel_groups(self) -> List[List[int]]:
        """获取并联组"""
        groups = []
        for series_pos in range(self.series_count):
            group = []
            for parallel_branch in range(self.parallel_count):
                cell_id = parallel_branch * self.series_count + series_pos
                group.append(cell_id)
            groups.append(group)
        return groups
    
    def get_cell_neighbors(self, cell_id: int, max_distance: float = 0.1) -> List[int]:
        """获取电芯的物理邻居"""
        if cell_id not in self.cell_positions:
            return []
        
        cell_pos = self.cell_positions[cell_id]
        neighbors = []
        
        for other_id, other_pos in self.cell_positions.items():
            if other_id == cell_id:
                continue
            
            # 计算欧几里得距离
            distance = np.sqrt(
                (cell_pos.x - other_pos.x)**2 + 
                (cell_pos.y - other_pos.y)**2 + 
                (cell_pos.z - other_pos.z)**2
            )
            
            if distance <= max_distance:
                neighbors.append(other_id)
        
        return neighbors
    
    def validate_topology(self) -> Dict[str, Any]:
        """验证拓扑结构的完整性"""
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'statistics': {}
        }
        
        # 检查电芯位置
        if len(self.cell_positions) != self.total_cells:
            validation_result['errors'].append(
                f"电芯位置不完整: {len(self.cell_positions)}/{self.total_cells}"
            )
            validation_result['is_valid'] = False
        
        # 检查电气连接
        series_groups = self.get_series_groups()
        for i, group in enumerate(series_groups):
            if len(group) != self.series_count:
                validation_result['errors'].append(f"串联组{i}电芯数量错误")
                validation_result['is_valid'] = False
        
        # 检查热连接
        if len(self.thermal_connections) == 0:
            validation_result['warnings'].append("没有定义热连接")
        
        # 统计信息
        validation_result['statistics'] = {
            'total_cells': self.total_cells,
            'thermal_connections': len(self.thermal_connections),
            'electrical_connections': len(self.electrical_connections),
            'series_groups': len(series_groups),
            'parallel_groups': len(self.get_parallel_groups())
        }
        
        return validation_result
    
    def export_topology_info(self) -> Dict[str, Any]:
        """导出拓扑信息"""
        return {
            'pack_name': self.pack_name,
            'configuration': f"{self.series_count}S{self.parallel_count}P",
            'total_cells': self.total_cells,
            'dimensions': self.pack_dimensions,
            'cooling_system': self.cooling_system,
            'cell_positions': {
                cell_id: {
                    'x': pos.x, 'y': pos.y, 'z': pos.z,
                    'row': pos.row, 'col': pos.col
                }
                for cell_id, pos in self.cell_positions.items()
            },
            'thermal_connections_count': len(self.thermal_connections),
            'electrical_connections_count': len(self.electrical_connections)
        }

    def to_dict(self) -> Dict[str, Any]:
        """
        序列化拓扑为字典 - 新增序列化支持

        Returns:
            包含完整拓扑信息的字典
        """
        return {
            'version': '1.0',  # 序列化版本
            'pack_name': self.pack_name,
            'series_count': self.series_count,
            'parallel_count': self.parallel_count,
            'total_cells': self.total_cells,
            'pack_dimensions': self.pack_dimensions,
            'cooling_system': self.cooling_system,
            'cell_positions': {
                str(cell_id): {
                    'x': pos.x, 'y': pos.y, 'z': pos.z,
                    'row': pos.row, 'col': pos.col
                }
                for cell_id, pos in self.cell_positions.items()
            },
            'thermal_connections': [
                {
                    'cell1_id': conn.cell1_id,
                    'cell2_id': conn.cell2_id,
                    'thermal_resistance': conn.thermal_resistance,
                    'contact_area': conn.contact_area
                }
                for conn in self.thermal_connections
            ],
            'electrical_connections': [
                {
                    'cell1_id': conn.cell1_id,
                    'cell2_id': conn.cell2_id,
                    'connection_type': conn.connection_type.value,
                    'resistance': conn.resistance,
                    'current_capacity': conn.current_capacity
                }
                for conn in self.electrical_connections
            ]
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PackTopology':
        """
        从字典反序列化拓扑 - 新增反序列化支持

        Args:
            data: 序列化的拓扑数据

        Returns:
            PackTopology实例

        Raises:
            ValueError: 当数据格式无效时
        """
        # 验证数据格式
        required_fields = ['series_count', 'parallel_count', 'pack_name']
        for field in required_fields:
            if field not in data:
                raise ValueError(f"缺少必需字段: {field}")

        # 创建拓扑实例
        topology = cls(
            series_count=data['series_count'],
            parallel_count=data['parallel_count'],
            pack_name=data['pack_name']
        )

        # 恢复包尺寸
        if 'pack_dimensions' in data and data['pack_dimensions']:
            topology.pack_dimensions = data['pack_dimensions']

        # 恢复冷却系统
        if 'cooling_system' in data and data['cooling_system']:
            topology.cooling_system = data['cooling_system']

        # 恢复电芯位置
        if 'cell_positions' in data:
            for cell_id_str, pos_data in data['cell_positions'].items():
                cell_id = int(cell_id_str)
                topology.set_cell_position(
                    cell_id,
                    pos_data['x'], pos_data['y'], pos_data['z'],
                    pos_data['row'], pos_data['col']
                )

        # 恢复热连接
        if 'thermal_connections' in data:
            for conn_data in data['thermal_connections']:
                topology.add_thermal_connection(
                    conn_data['cell1_id'], conn_data['cell2_id'],
                    conn_data['thermal_resistance'], conn_data['contact_area']
                )

        # 恢复电气连接
        if 'electrical_connections' in data:
            for conn_data in data['electrical_connections']:
                conn_type = CellConnectionType(conn_data['connection_type'])
                topology.add_electrical_connection(
                    conn_data['cell1_id'], conn_data['cell2_id'],
                    conn_type, conn_data['resistance'], conn_data['current_capacity']
                )

        return topology

    def save_to_file(self, filepath: str) -> None:
        """
        保存拓扑到文件 - 新增文件保存功能

        Args:
            filepath: 保存路径
        """
        import json

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.to_dict(), f, indent=2, ensure_ascii=False)
            logging.info(f"拓扑已保存到: {filepath}")
        except Exception as e:
            logging.error(f"保存拓扑失败: {e}")
            raise

    @classmethod
    def load_from_file(cls, filepath: str) -> 'PackTopology':
        """
        从文件加载拓扑 - 新增文件加载功能

        Args:
            filepath: 文件路径

        Returns:
            PackTopology实例
        """
        import json

        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)

            topology = cls.from_dict(data)
            logging.info(f"拓扑已从文件加载: {filepath}")
            return topology

        except Exception as e:
            logging.error(f"加载拓扑失败: {e}")
            raise

    def _generate_smart_connections(self) -> None:
        """
        智能生成电气和热连接 - 新增方法
        根据拓扑结构自动生成合理的连接
        """
        # 生成电气连接
        self._generate_electrical_connections()

        # 生成热连接
        self._generate_thermal_connections()

    def _generate_electrical_connections(self) -> None:
        """生成电气连接"""
        # 串联连接：连接同一并联支路内的相邻电芯
        for parallel_branch in range(self.parallel_count):
            for series_pos in range(self.series_count - 1):
                cell1_id = series_pos * self.parallel_count + parallel_branch
                cell2_id = (series_pos + 1) * self.parallel_count + parallel_branch

                # 添加串联连接
                self.add_electrical_connection(
                    cell1_id, cell2_id,
                    CellConnectionType.SERIES,
                    resistance=0.001,  # 1mΩ连接电阻
                    current_capacity=200.0  # 200A载流能力
                )

        # 并联连接：连接同一串联位置的不同支路
        if self.parallel_count > 1:
            for series_pos in range(self.series_count):
                for parallel_branch in range(self.parallel_count - 1):
                    cell1_id = series_pos * self.parallel_count + parallel_branch
                    cell2_id = series_pos * self.parallel_count + (parallel_branch + 1)

                    # 添加并联连接
                    self.add_electrical_connection(
                        cell1_id, cell2_id,
                        CellConnectionType.PARALLEL,
                        resistance=0.0005,  # 0.5mΩ并联连接电阻
                        current_capacity=300.0  # 300A载流能力
                    )

    def _generate_thermal_connections(self) -> None:
        """生成热连接"""
        # 基于物理位置生成热连接
        for cell1_id, pos1 in self.cell_positions.items():
            for cell2_id, pos2 in self.cell_positions.items():
                if cell1_id >= cell2_id:  # 避免重复连接
                    continue

                # 计算物理距离
                distance = ((pos1.x - pos2.x)**2 + (pos1.y - pos2.y)**2)**0.5

                # 只连接相邻的电芯
                if distance < 0.1:  # 10cm内认为相邻
                    # 根据距离确定热阻
                    if distance < 0.03:  # 3cm内，紧密相邻
                        thermal_resistance = 0.5  # 低热阻
                        contact_area = 0.001  # 1000mm²接触面积
                    elif distance < 0.07:  # 7cm内，一般相邻
                        thermal_resistance = 1.0  # 中等热阻
                        contact_area = 0.0005  # 500mm²接触面积
                    else:  # 较远相邻
                        thermal_resistance = 2.0  # 高热阻
                        contact_area = 0.0002  # 200mm²接触面积

                    self.add_thermal_connection(
                        cell1_id, cell2_id,
                        thermal_resistance, contact_area
                    )


class StandardPackTopologies:
    """标准电池包拓扑配置"""
    
    @staticmethod
    def create_16s1p_linear() -> PackTopology:
        """创建16S1P线性排列电池包"""
        topology = PackTopology(16, 1, "16S1P Linear Pack")
        
        # 线性排列电芯位置
        cell_length = 0.065  # 电芯长度
        cell_spacing = 0.002  # 电芯间距
        
        for i in range(16):
            x = i * (cell_length + cell_spacing)
            topology.set_cell_position(i, x, 0.0, 0.0, 0, i)
        
        # 添加串联电气连接
        for i in range(15):
            topology.add_electrical_connection(
                i, i+1, CellConnectionType.SERIES, 
                resistance=0.0005, current_capacity=200.0
            )
        
        # 添加相邻电芯热连接
        for i in range(15):
            topology.add_thermal_connection(
                i, i+1, thermal_resistance=2.0, contact_area=0.001
            )
        
        # 设置包尺寸
        topology.pack_dimensions = {
            'length': 16 * (cell_length + cell_spacing),
            'width': 0.1,
            'height': 0.065
        }
        
        return topology
    
    @staticmethod
    def create_16s2p_matrix() -> PackTopology:
        """创建16S2P矩阵排列电池包"""
        topology = PackTopology(16, 2, "16S2P Matrix Pack")
        
        # 矩阵排列 (2行16列)
        cell_length = 0.065
        cell_width = 0.018
        cell_spacing_x = 0.002
        cell_spacing_y = 0.005
        
        for parallel_branch in range(2):
            for series_pos in range(16):
                cell_id = parallel_branch * 16 + series_pos
                x = series_pos * (cell_length + cell_spacing_x)
                y = parallel_branch * (cell_width + cell_spacing_y)
                
                topology.set_cell_position(cell_id, x, y, 0.0, parallel_branch, series_pos)
        
        # 添加串联连接 (每个并联支路内部)
        for branch in range(2):
            for i in range(15):
                cell1 = branch * 16 + i
                cell2 = branch * 16 + i + 1
                topology.add_electrical_connection(
                    cell1, cell2, CellConnectionType.SERIES,
                    resistance=0.0005, current_capacity=200.0
                )
        
        # 添加并联连接 (相同串联位置的电芯)
        for series_pos in range(16):
            cell1 = 0 * 16 + series_pos  # 第一支路
            cell2 = 1 * 16 + series_pos  # 第二支路
            topology.add_electrical_connection(
                cell1, cell2, CellConnectionType.PARALLEL,
                resistance=0.001, current_capacity=400.0
            )
        
        # 添加热连接
        # 同行相邻电芯
        for branch in range(2):
            for i in range(15):
                cell1 = branch * 16 + i
                cell2 = branch * 16 + i + 1
                topology.add_thermal_connection(cell1, cell2, 1.5, 0.001)
        
        # 同列相邻电芯 (上下)
        for series_pos in range(16):
            cell1 = 0 * 16 + series_pos
            cell2 = 1 * 16 + series_pos
            topology.add_thermal_connection(cell1, cell2, 3.0, 0.0005)
        
        return topology
    
    @staticmethod
    def create_custom_pack(series_count: int, parallel_count: int,
                          layout_type: str = "matrix") -> PackTopology:
        """
        创建自定义电池包拓扑 - 改进: 智能连接生成

        Args:
            series_count: 串联数量
            parallel_count: 并联数量
            layout_type: 布局类型 ("linear" 或 "matrix")

        Returns:
            PackTopology实例
        """
        pack_name = f"{series_count}S{parallel_count}P Custom Pack"
        topology = PackTopology(series_count, parallel_count, pack_name)

        # 智能布局生成
        if layout_type == "linear" and parallel_count == 1:
            # 线性排列 - 适用于纯串联
            cell_spacing = 0.067
            for i in range(topology.total_cells):
                x = i * cell_spacing
                y = 0.0
                topology.set_cell_position(i, x, y, 0.0, 0, i)

        elif layout_type == "matrix" or parallel_count > 1:
            # 矩阵排列 - 适用于串并联混合
            cell_spacing_x = 0.067  # 串联方向间距
            cell_spacing_y = 0.023  # 并联方向间距

            for series_pos in range(series_count):
                for parallel_branch in range(parallel_count):
                    cell_id = series_pos * parallel_count + parallel_branch
                    x = series_pos * cell_spacing_x
                    y = parallel_branch * cell_spacing_y
                    topology.set_cell_position(cell_id, x, y, 0.0, parallel_branch, series_pos)

        # 智能连接生成 - 新增功能
        topology._generate_smart_connections()

        return topology

    @staticmethod
    def create_8s2p_matrix() -> PackTopology:
        """创建8S2P矩阵排列电池包"""
        topology = PackTopology(series_count=8, parallel_count=2, pack_name="8S2P Matrix Pack")

        # 8串2并，总共16个电芯 - 设置电芯位置
        for s in range(8):
            for p in range(2):
                cell_id = s * 2 + p
                # 设置电芯物理位置 (2x8矩阵)
                x = s * 0.1  # 串联方向间距10cm
                y = p * 0.05  # 并联方向间距5cm
                z = 0.0
                topology.set_cell_position(cell_id, x, y, z, row=p, col=s)

        # 串联连接 (同一并联支路内的串联)
        for s in range(7):
            for p in range(2):
                cell1 = s * 2 + p
                cell2 = (s + 1) * 2 + p
                topology.add_electrical_connection(cell1, cell2, CellConnectionType.SERIES)

        # 并联连接 (同一串联位置的并联)
        for s in range(8):
            cell1 = s * 2
            cell2 = s * 2 + 1
            topology.add_electrical_connection(cell1, cell2, CellConnectionType.PARALLEL)

        # 热连接（相邻电芯）
        for s in range(8):
            for p in range(2):
                cell_id = s * 2 + p
                # 与同串另一个电芯热连接
                if p == 0:
                    topology.add_thermal_connection(cell_id, cell_id + 1, thermal_resistance=0.5)
                # 与相邻串电芯热连接
                if s < 7:
                    next_cell = (s + 1) * 2 + p
                    topology.add_thermal_connection(cell_id, next_cell, thermal_resistance=1.0)

        return topology

    @staticmethod
    def create_4s4p_matrix() -> PackTopology:
        """创建4S4P矩阵排列电池包"""
        topology = PackTopology(series_count=4, parallel_count=4, pack_name="4S4P Matrix Pack")

        # 4串4并，总共16个电芯 - 设置电芯位置
        for s in range(4):
            for p in range(4):
                cell_id = s * 4 + p
                # 设置电芯物理位置 (4x4矩阵)
                x = s * 0.1  # 串联方向间距10cm
                y = p * 0.05  # 并联方向间距5cm
                z = 0.0
                topology.set_cell_position(cell_id, x, y, z, row=s, col=p)

        # 串联连接 (同一并联支路内的串联)
        for s in range(3):
            for p in range(4):
                cell1 = s * 4 + p
                cell2 = (s + 1) * 4 + p
                topology.add_electrical_connection(cell1, cell2, CellConnectionType.SERIES)

        # 并联连接 (同一串联位置的并联)
        for s in range(4):
            for p in range(3):
                cell1 = s * 4 + p
                cell2 = s * 4 + p + 1
                topology.add_electrical_connection(cell1, cell2, CellConnectionType.PARALLEL)

        # 热连接（网格状）
        for s in range(4):
            for p in range(4):
                cell_id = s * 4 + p
                # 与右侧电芯热连接
                if p < 3:
                    topology.add_thermal_connection(cell_id, cell_id + 1, thermal_resistance=0.5)
                # 与下方电芯热连接
                if s < 3:
                    next_cell = (s + 1) * 4 + p
                    topology.add_thermal_connection(cell_id, next_cell, thermal_resistance=0.5)

        return topology

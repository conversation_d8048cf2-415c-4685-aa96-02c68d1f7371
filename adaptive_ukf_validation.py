#!/usr/bin/env python3
"""
自适应参数估算UKF验证脚本

重新启用参数估算，但使用更稳定的配置：
1. 启用内阻参数估算
2. 使用更保守的参数估算噪声
3. 强化参数约束
4. 渐进式参数调整
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt
import time

# 添加项目路径
sys.path.append('ukf_estimator')
sys.path.append('battery_simulator')

def main():
    """Main validation function with adaptive parameter estimation"""
    print("🔋 自适应参数估算UKF验证")
    print("=" * 60)
    
    try:
        # 导入模块
        from battery_simulator.cell_simulator import CellSimulator
        from ukf_estimator import UKFCore, UKFConfig, FirstOrderRCModel, BatteryParameters
        from ukf_estimator.state_estimator import BatteryStateEstimator
        
        print("✅ 模块导入成功")

        # 创建自适应参数估算配置
        print("\n🔧 创建自适应参数估算UKF配置...")
        
        # 保守的参数估算UKF配置
        ukf_config = UKFConfig(
            alpha=1e-4,      # 更小的alpha值，提高稳定性
            beta=2.0,
            kappa=0.0,
            process_noise_std={
                'soc': 2e-7,     # 极小的SOC过程噪声
                'v1': 1e-6,      # 极小的极化电压过程噪声
                'r0': 1e-8,      # 极小的欧姆内阻过程噪声
                'r1': 1e-8,      # 极小的极化内阻过程噪声
                'c1': 1e-9,      # 极小的极化电容过程噪声
            },
            measurement_noise_std=5e-3,  # 5mV测量噪声
            thread_safe=False
        )
        
        # 改进的电池参数，启用参数估算
        battery_params = BatteryParameters(
            nominal_capacity=280.0,
            nominal_voltage=3.2,
            r0_initial=0.070,    # 接近PyBaMM平均值
            r1_initial=0.015,    # 较小的极化内阻
            c1_initial=3000.0,
            # 紧缩的参数范围，防止发散
            r0_min=0.020,        # 20mΩ
            r0_max=0.120,        # 120mΩ
            r1_min=0.005,        # 5mΩ
            r1_max=0.040,        # 40mΩ
            c1_min=1500.0,       # 1500F
            c1_max=5000.0,       # 5000F
            # 使用修正的SOC-OCV表
            soc_ocv_table={
                0.0: 3.3, 0.05: 3.4, 0.1: 3.607, 0.15: 3.64, 0.2: 3.674, 
                0.25: 3.7, 0.3: 3.725, 0.35: 3.77, 0.4: 3.815, 0.45: 3.9,
                0.5: 3.991, 0.55: 4.07, 0.6: 4.152, 0.65: 4.21, 0.7: 4.279, 
                0.75: 4.34, 0.8: 4.405, 0.85: 4.46, 0.9: 4.525, 0.95: 4.56, 1.0: 4.6
            }
        )
        
        # 创建模型和估算器 - 启用参数估算
        battery_model = FirstOrderRCModel(battery_params, estimate_parameters=True)
        ukf_core = UKFCore(ukf_config)
        
        # 创建虚拟电池
        cell = CellSimulator(cell_id=1, chemistry="LiFePO4")
        cell.set_initial_conditions(initial_soc=0.6, initial_temperature=25.0)
        
        # 创建状态估算器
        estimator = BatteryStateEstimator(battery_model, ukf_core)
        
        # 初始化估算器 - 使用更小的初始不确定性
        estimator.initialize(
            initial_soc=0.6, 
            initial_temperature=25.0,
            initial_covariance_scale=0.01  # 很小的初始不确定性
        )
        
        print("✅ 自适应参数估算UKF系统创建成功")
        print("   - 启用内阻参数估算")
        print("   - 使用极小的过程噪声")
        print("   - 强化参数约束范围")
        
        # 运行验证
        print("\n🧪 开始自适应参数估算验证仿真...")
        results = run_adaptive_validation(cell, estimator, ukf_config)

        # 分析结果
        print("\n📊 分析自适应参数估算结果...")
        analyze_adaptive_results(results)

        # 创建可视化
        print("\n📈 创建自适应参数估算可视化图表...")
        create_adaptive_plots(results)

        print("\n✅ 自适应参数估算UKF验证完成!")
        return True

    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_adaptive_validation(cell, estimator, ukf_config):
    """运行自适应参数估算验证仿真"""
    # 仿真参数
    total_steps = 80
    dt = 10.0  # 10秒时间步长
    
    # 温和的电流剖面
    current_profile = []
    
    # 阶段1: 小电流充电 (0-20步)
    current_profile.extend([0.5] * 20)
    
    # 阶段2: 静置 (20-30步)
    current_profile.extend([0.0] * 10)
    
    # 阶段3: 小电流放电 (30-50步)
    current_profile.extend([-1.0] * 20)
    
    # 阶段4: 静置 (50-60步)
    current_profile.extend([0.0] * 10)
    
    # 阶段5: 中等电流放电 (60-80步)
    current_profile.extend([-2.0] * 20)
    
    # 存储结果
    results = {
        'times': [],
        'currents': [],
        'pybamm_soc': [],
        'pybamm_voltage': [],
        'pybamm_resistance': [],
        'ukf_soc': [],
        'ukf_voltage_pred': [],
        'ukf_v1': [],
        'ukf_r0': [],
        'ukf_r1': [],
        'ukf_c1': [],
        'ukf_total_resistance': [],
        'ukf_soc_uncertainty': [],
        'ukf_r0_uncertainty': [],
        'soc_error': [],
        'voltage_error': [],
        'resistance_error': [],
        'coulomb_counting_soc': [],
        'parameter_stability': []  # 参数稳定性指标
    }
    
    print(f"  🔄 运行 {total_steps} 步自适应参数估算仿真...")
    
    # 库仑计法SOC跟踪
    coulomb_soc = 0.6  # 初始SOC
    
    # 参数稳定性跟踪
    r0_history = []
    r1_history = []

    for step in range(total_steps):
        current = current_profile[step]

        # PyBaMM仿真
        cell_state = cell.simulate_step(current, 25.0, dt)

        # UKF估算
        estimation = estimator.estimate_step(
            voltage=cell_state.voltage,
            current=cell_state.current,
            temperature=cell_state.temperature,
            dt=dt
        )
        
        # 库仑计法SOC计算
        dsoc_coulomb = current * dt / (3600.0 * 280.0)
        coulomb_soc = np.clip(coulomb_soc + dsoc_coulomb, 0.0, 1.0)
        
        # 参数稳定性监控
        r0_history.append(estimation.r0)
        r1_history.append(estimation.r1)
        if len(r0_history) > 10:
            r0_history.pop(0)
            r1_history.pop(0)
        
        # 计算参数稳定性 (最近10步的标准差)
        if len(r0_history) >= 5:
            r0_stability = np.std(r0_history[-5:]) / np.mean(r0_history[-5:])
            r1_stability = np.std(r1_history[-5:]) / np.mean(r1_history[-5:])
            param_stability = (r0_stability + r1_stability) / 2
        else:
            param_stability = 0.0

        # 记录结果
        results['times'].append(step * dt / 60.0)
        results['currents'].append(current)
        
        # PyBaMM结果
        results['pybamm_soc'].append(cell_state.soc)
        results['pybamm_voltage'].append(cell_state.voltage)
        pybamm_resistance = cell_state.internal_resistance or 0.070
        results['pybamm_resistance'].append(pybamm_resistance)
        
        # UKF结果
        results['ukf_soc'].append(estimation.soc)
        results['ukf_voltage_pred'].append(estimation.predicted_voltage)
        results['ukf_v1'].append(estimation.v1 if hasattr(estimation, 'v1') else 0.0)
        results['ukf_r0'].append(estimation.r0)
        results['ukf_r1'].append(estimation.r1)
        results['ukf_c1'].append(estimation.c1 if hasattr(estimation, 'c1') else 3000.0)
        results['ukf_total_resistance'].append(estimation.r0 + estimation.r1)
        results['ukf_soc_uncertainty'].append(estimation.soc_uncertainty)
        results['ukf_r0_uncertainty'].append(estimation.r0_uncertainty if hasattr(estimation, 'r0_uncertainty') else 0.001)
        
        # 库仑计法结果
        results['coulomb_counting_soc'].append(coulomb_soc)
        
        # 误差计算
        results['soc_error'].append(abs(estimation.soc - cell_state.soc))
        results['voltage_error'].append(abs(estimation.predicted_voltage - cell_state.voltage))
        results['resistance_error'].append(abs((estimation.r0 + estimation.r1) - pybamm_resistance))
        results['parameter_stability'].append(param_stability)

        # 显示进度
        if (step + 1) % 20 == 0:
            print(f"    步骤 {step+1}/{total_steps}: "
                  f"PyBaMM SOC={cell_state.soc:.3f}, "
                  f"UKF SOC={estimation.soc:.3f}, "
                  f"SOC误差={abs(estimation.soc - cell_state.soc):.4f}, "
                  f"电压误差={abs(estimation.predicted_voltage - cell_state.voltage)*1000:.1f}mV, "
                  f"PyBaMM R={pybamm_resistance*1000:.1f}mΩ, "
                  f"UKF R={(estimation.r0 + estimation.r1)*1000:.1f}mΩ, "
                  f"R0={estimation.r0*1000:.1f}mΩ, "
                  f"R1={estimation.r1*1000:.1f}mΩ")
    
    # 转换为numpy数组
    for key in results:
        results[key] = np.array(results[key])
    
    return results

def analyze_adaptive_results(results):
    """分析自适应参数估算结果"""
    # SOC性能指标
    soc_mae = np.mean(results['soc_error'])
    soc_rmse = np.sqrt(np.mean(results['soc_error']**2))
    soc_max_error = np.max(results['soc_error'])

    # 电压性能指标
    voltage_mae = np.mean(results['voltage_error'])
    voltage_rmse = np.sqrt(np.mean(results['voltage_error']**2))

    # 内阻性能指标
    resistance_mae = np.mean(results['resistance_error']) * 1000  # mΩ
    resistance_rmse = np.sqrt(np.mean(results['resistance_error']**2)) * 1000  # mΩ
    
    # 平均值对比
    pybamm_r_avg = np.mean(results['pybamm_resistance']) * 1000  # mΩ
    ukf_r_avg = np.mean(results['ukf_total_resistance']) * 1000  # mΩ
    ukf_r0_avg = np.mean(results['ukf_r0']) * 1000  # mΩ
    ukf_r1_avg = np.mean(results['ukf_r1']) * 1000  # mΩ

    # 库仑计法对比
    coulomb_error = np.abs(results['coulomb_counting_soc'] - results['pybamm_soc'])
    coulomb_mae = np.mean(coulomb_error)
    coulomb_rmse = np.sqrt(np.mean(coulomb_error**2))

    # 参数稳定性分析
    param_stability_avg = np.mean(results['parameter_stability'])
    r0_variation = (np.max(results['ukf_r0']) - np.min(results['ukf_r0'])) * 1000  # mΩ
    r1_variation = (np.max(results['ukf_r1']) - np.min(results['ukf_r1'])) * 1000  # mΩ

    # 后半段性能分析
    second_half_indices = len(results['soc_error']) // 2
    second_half_soc_error = results['soc_error'][second_half_indices:]
    second_half_mae = np.mean(second_half_soc_error)
    second_half_rmse = np.sqrt(np.mean(second_half_soc_error**2))

    print(f"🎯 自适应参数估算UKF验证结果分析:")
    print(f"")
    print(f"📈 SOC Estimation Performance:")
    print(f"  - Mean Absolute Error (MAE): {soc_mae:.4f} ({soc_mae*100:.2f}%)")
    print(f"  - Root Mean Square Error (RMSE): {soc_rmse:.4f} ({soc_rmse*100:.2f}%)")
    print(f"  - Maximum Error: {soc_max_error:.4f} ({soc_max_error*100:.2f}%)")
    print(f"  - Second Half MAE: {second_half_mae:.4f} ({second_half_mae*100:.2f}%)")
    print(f"  - Second Half RMSE: {second_half_rmse:.4f} ({second_half_rmse*100:.2f}%)")
    print(f"")
    print(f"⚡ Voltage Prediction Performance:")
    print(f"  - Mean Absolute Error: {voltage_mae:.4f} V ({voltage_mae*1000:.1f} mV)")
    print(f"  - Root Mean Square Error: {voltage_rmse:.4f} V ({voltage_rmse*1000:.1f} mV)")
    print(f"")
    print(f"🔧 Resistance Estimation Performance:")
    print(f"  - PyBaMM Average Resistance: {pybamm_r_avg:.2f} mΩ")
    print(f"  - UKF Average Total Resistance: {ukf_r_avg:.2f} mΩ")
    print(f"  - UKF Average R0 (Ohmic): {ukf_r0_avg:.2f} mΩ")
    print(f"  - UKF Average R1 (Polarization): {ukf_r1_avg:.2f} mΩ")
    print(f"  - Resistance MAE: {resistance_mae:.2f} mΩ")
    print(f"  - Resistance RMSE: {resistance_rmse:.2f} mΩ")
    print(f"  - Resistance Relative Error: {abs(ukf_r_avg - pybamm_r_avg)/pybamm_r_avg*100:.1f}%")
    print(f"")
    print(f"📊 Parameter Adaptation Analysis:")
    print(f"  - R0 Variation Range: {r0_variation:.2f} mΩ")
    print(f"  - R1 Variation Range: {r1_variation:.2f} mΩ")
    print(f"  - Average Parameter Stability: {param_stability_avg:.4f}")
    print(f"")
    print(f"🔋 Coulomb Counting Comparison:")
    print(f"  - Coulomb Counting MAE: {coulomb_mae:.4f} ({coulomb_mae*100:.2f}%)")
    print(f"  - Coulomb Counting RMSE: {coulomb_rmse:.4f} ({coulomb_rmse*100:.2f}%)")

    # 性能评级
    print(f"")
    print(f"🏆 Performance Rating:")
    
    if soc_rmse < 0.01:
        soc_rating = "Excellent (RMSE < 1%)"
    elif soc_rmse < 0.02:
        soc_rating = "Good (RMSE < 2%)"
    elif soc_rmse < 0.05:
        soc_rating = "Fair (RMSE < 5%)"
    else:
        soc_rating = "Needs Improvement (RMSE >= 5%)"
    print(f"  SOC Estimation: {soc_rating}")
    
    if voltage_rmse < 0.01:
        voltage_rating = "Excellent (RMSE < 10mV)"
    elif voltage_rmse < 0.03:
        voltage_rating = "Good (RMSE < 30mV)"
    elif voltage_rmse < 0.05:
        voltage_rating = "Fair (RMSE < 50mV)"
    else:
        voltage_rating = "Needs Improvement (RMSE >= 50mV)"
    print(f"  Voltage Prediction: {voltage_rating}")
    
    resistance_relative_error = abs(ukf_r_avg - pybamm_r_avg)/pybamm_r_avg*100
    if resistance_relative_error < 10:
        resistance_rating = "Excellent (Relative Error < 10%)"
    elif resistance_relative_error < 20:
        resistance_rating = "Good (Relative Error < 20%)"
    elif resistance_relative_error < 50:
        resistance_rating = "Fair (Relative Error < 50%)"
    else:
        resistance_rating = "Needs Improvement (Relative Error >= 50%)"
    print(f"  Resistance Estimation: {resistance_rating}")
    
    if param_stability_avg < 0.05:
        stability_rating = "Excellent (Very Stable)"
    elif param_stability_avg < 0.1:
        stability_rating = "Good (Stable)"
    elif param_stability_avg < 0.2:
        stability_rating = "Fair (Moderately Stable)"
    else:
        stability_rating = "Needs Improvement (Unstable)"
    print(f"  Parameter Stability: {stability_rating}")

    # 与库仑计法对比
    if soc_rmse < coulomb_rmse:
        print(f"  🎉 UKF性能优于库仑计法!")
    else:
        print(f"  ⚠️ UKF性能不如库仑计法，需要进一步优化")

def create_adaptive_plots(results):
    """创建自适应参数估算可视化图表"""
    # 创建主要对比图
    fig, axes = plt.subplots(3, 2, figsize=(14, 16))
    fig.suptitle('Adaptive Parameter Estimation UKF Results', fontsize=16, fontweight='bold')
    
    times = results['times']
    
    # 1. SOC对比
    axes[0, 0].plot(times, results['pybamm_soc'], 'r-', linewidth=2, label='PyBaMM (True Value)')
    axes[0, 0].plot(times, results['ukf_soc'], 'b--', linewidth=2, label='UKF Estimation')
    axes[0, 0].plot(times, results['coulomb_counting_soc'], 'g:', linewidth=2, label='Coulomb Counting')
    axes[0, 0].fill_between(times, 
                           results['ukf_soc'] - results['ukf_soc_uncertainty'],
                           results['ukf_soc'] + results['ukf_soc_uncertainty'],
                           alpha=0.3, color='blue', label='UKF Uncertainty')
    axes[0, 0].set_ylabel('SOC')
    axes[0, 0].set_title('SOC Estimation Comparison')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # 2. 电压对比
    axes[0, 1].plot(times, results['pybamm_voltage'], 'r-', linewidth=2, label='PyBaMM Actual Voltage')
    axes[0, 1].plot(times, results['ukf_voltage_pred'], 'b--', linewidth=2, label='UKF Predicted Voltage')
    axes[0, 1].set_ylabel('Voltage (V)')
    axes[0, 1].set_title('Voltage Prediction Comparison')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # 3. 内阻对比和演化
    axes[1, 0].plot(times, results['pybamm_resistance'] * 1000, 'r-', linewidth=2, label='PyBaMM Resistance')
    axes[1, 0].plot(times, results['ukf_r0'] * 1000, 'g--', linewidth=2, label='UKF R0 (Ohmic)')
    axes[1, 0].plot(times, results['ukf_r1'] * 1000, 'orange', linewidth=2, label='UKF R1 (Polarization)')
    axes[1, 0].plot(times, results['ukf_total_resistance'] * 1000, 'purple', linewidth=2, alpha=0.8, label='UKF Total')
    axes[1, 0].set_ylabel('Resistance (mOhm)')
    axes[1, 0].set_title('Resistance Evolution')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)

    # 4. 参数稳定性
    axes[1, 1].plot(times, results['parameter_stability'], 'purple', linewidth=2, label='Parameter Stability')
    axes[1, 1].set_ylabel('Stability Index')
    axes[1, 1].set_title('Parameter Stability')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)

    # 5. SOC误差对比
    axes[2, 0].plot(times, results['soc_error'] * 100, 'purple', linewidth=2, label='UKF SOC Error (%)')
    coulomb_error = np.abs(results['coulomb_counting_soc'] - results['pybamm_soc'])
    axes[2, 0].plot(times, coulomb_error * 100, 'green', linewidth=2, label='Coulomb Counting Error (%)')
    axes[2, 0].set_ylabel('SOC Error (%)')
    axes[2, 0].set_xlabel('Time (minutes)')
    axes[2, 0].set_title('SOC Estimation Error Comparison')
    axes[2, 0].legend()
    axes[2, 0].grid(True, alpha=0.3)

    # 6. 内阻误差
    axes[2, 1].plot(times, results['resistance_error'] * 1000, 'red', linewidth=2, label='Resistance Error (mOhm)')
    axes[2, 1].set_ylabel('Resistance Error (mOhm)')
    axes[2, 1].set_xlabel('Time (minutes)')
    axes[2, 1].set_title('Resistance Estimation Error')
    axes[2, 1].legend()
    axes[2, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig("adaptive_ukf_validation_results.png", dpi=300, bbox_inches='tight')

    print("📊 Charts saved:")
    print("  - adaptive_ukf_validation_results.png (Adaptive parameter estimation results)")

    # 显示图表
    plt.show()

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 自适应参数估算UKF验证成功!")
        print("\n📋 自适应参数估算总结:")
        print("  ✅ 重新启用内阻参数估算")
        print("  ✅ 使用极小的过程噪声确保稳定性")
        print("  ✅ 强化参数约束防止发散")
        print("  ✅ 添加参数稳定性监控")
        print("  ✅ 显示内阻参数的动态演化")
        print("\n🚀 现在UKF内阻可以动态适应PyBaMM的变化!")
    else:
        print("\n❌ 自适应参数估算UKF验证失败!")
        sys.exit(1)

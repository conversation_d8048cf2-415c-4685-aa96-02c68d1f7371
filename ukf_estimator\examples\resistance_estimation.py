#!/usr/bin/env python3
"""
内阻估算示例

专门演示UKF估算器的内阻估算功能，包括欧姆内阻和极化内阻。
展示参数辨识和在线自适应估算能力。
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt
import logging

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'battery_simulator'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def main():
    """主函数"""
    print("⚡ UKF内阻估算示例")
    print("=" * 60)
    
    try:
        # 1. 导入模块
        from battery_simulator.cell_simulator import CellSimulator
        from ukf_estimator import (
            UKFCore, UKFConfig,
            FirstOrderRCModel, BatteryParameters,
            BatteryStateEstimator,
            VirtualBatteryInterface,
            ParameterIdentifier,
            EstimationVisualizer,
            EstimationValidator
        )
        from ukf_estimator.data_interface import MeasurementData
        
        print("✅ 模块导入成功")
        
        # 2. 创建虚拟电池 (模拟内阻变化)
        print("\n📦 创建虚拟电池系统...")
        cell = CellSimulator(cell_id=1, chemistry="LiFePO4", include_thermal=True)
        cell.set_initial_conditions(initial_soc=0.5, initial_temperature=25.0)
        print(f"✅ 虚拟电池创建成功")
        
        # 3. 配置UKF参数 (针对内阻估算优化)
        print("\n🧮 配置UKF参数...")
        ukf_config = UKFConfig(
            alpha=1e-3,
            beta=2.0,
            kappa=0.0,
            process_noise_std={
                'soc': 1e-5,     # 降低SOC过程噪声
                'v1': 1e-4,      # 降低极化电压过程噪声
                'r0': 1e-5,      # 适中的R0过程噪声
                'r1': 1e-5,      # 适中的R1过程噪声
                'c1': 1e-3,      # 较大的C1过程噪声
            },
            measurement_noise_std=2e-3  # 2mV测量噪声
        )
        
        # 4. 创建电池模型 (启用参数估算)
        battery_params = BatteryParameters(
            nominal_capacity=280.0,
            nominal_voltage=3.2,
            r0_initial=0.004,    # 初始猜测值
            r1_initial=0.003,    # 初始猜测值
            c1_initial=2000.0,   # 初始猜测值
        )
        
        battery_model = FirstOrderRCModel(battery_params, estimate_parameters=True)
        ukf_core = UKFCore(ukf_config)
        
        # 5. 创建数据接口和估算器
        data_interface = VirtualBatteryInterface(cell)
        estimator = BatteryStateEstimator(battery_model, ukf_core, data_interface)
        estimator.initialize(initial_soc=0.5, initial_temperature=25.0)
        
        print("✅ UKF估算器配置完成")
        
        # 6. 创建参数辨识器
        param_identifier = ParameterIdentifier(battery_model)
        
        # 7. 运行内阻估算仿真
        print("\n🔄 开始内阻估算仿真...")
        
        total_steps = 300
        dt = 5.0  # 5秒步长，更高频率
        
        # 设计激励信号 (用于内阻辨识)
        current_profile = []
        for step in range(total_steps):
            if step < 50:
                # 阶跃响应测试
                current = 5.0 if step % 10 < 5 else 0.0
            elif step < 100:
                # 正弦波激励
                current = 3.0 * np.sin(2 * np.pi * step / 20)
            elif step < 150:
                # 脉冲激励
                current = 4.0 if step % 15 < 3 else -2.0
            elif step < 200:
                # 随机激励
                current = np.random.normal(0, 2.0)
            else:
                # 恒流测试
                current = 2.0
            
            current_profile.append(current)
        
        # 存储结果
        estimation_results = []
        ground_truth_data = []
        parameter_history = []
        
        print(f"运行{total_steps}步仿真，专注内阻估算...")
        
        for step in range(total_steps):
            current = current_profile[step]
            
            # 虚拟电池仿真
            cell_state = cell.simulate_step(current, 25.0, dt)
            
            # UKF估算
            estimation = estimator.estimate_step(
                voltage=cell_state.voltage,
                current=cell_state.current,
                temperature=cell_state.temperature,
                dt=dt
            )
            
            # 保存结果
            estimation_results.append(estimation)
            
            # 保存真实值
            ground_truth = MeasurementData(
                timestamp=cell_state.timestamp,
                voltage=cell_state.voltage,
                current=cell_state.current,
                temperature=cell_state.temperature,
                soc_true=cell_state.soc,
                r0_true=cell_state.internal_resistance,
                is_valid=cell_state.is_valid
            )
            ground_truth_data.append(ground_truth)
            
            # 记录参数历史
            parameter_history.append({
                'step': step,
                'r0_est': estimation.r0,
                'r1_est': estimation.r1,
                'c1_est': estimation.c1,
                'r0_true': cell_state.internal_resistance,
                'r0_uncertainty': estimation.r0_uncertainty
            })
            
            # 每50步进行在线参数辨识
            if step > 50 and step % 50 == 0:
                recent_data = ground_truth_data[-50:]
                current_params = {
                    'r0': estimation.r0,
                    'r1': estimation.r1,
                    'c1': estimation.c1
                }
                
                updated_params = param_identifier.identify_online(recent_data, current_params)
                
                if updated_params != current_params:
                    print(f"  步骤 {step}: 参数更新 - "
                          f"R0: {current_params['r0']:.4f} → {updated_params['r0']:.4f}Ω")
            
            # 显示进度
            if (step + 1) % 60 == 0:
                print(f"  步骤 {step+1}/{total_steps}: "
                      f"R0估算={estimation.r0*1000:.2f}mΩ, "
                      f"R0真实={cell_state.internal_resistance*1000:.2f}mΩ, "
                      f"误差={abs(estimation.r0 - cell_state.internal_resistance)*1000:.2f}mΩ")
        
        print("✅ 内阻估算仿真完成")
        
        # 8. 离线参数辨识验证
        print("\n🔍 执行离线参数辨识...")
        try:
            identified_params = param_identifier.identify_offline(
                ground_truth_data[50:200],  # 使用中间段数据
                method="differential_evolution"
            )
            
            print(f"离线辨识结果:")
            print(f"  - R0: {identified_params['r0']*1000:.2f} mΩ")
            print(f"  - R1: {identified_params['r1']*1000:.2f} mΩ")
            print(f"  - C1: {identified_params['c1']:.1f} F")
            print(f"  - 拟合误差: {identified_params['cost']:.4f}")
            
        except Exception as e:
            print(f"⚠️  离线参数辨识失败: {e}")
            identified_params = None
        
        # 9. 性能评估
        print("\n📊 内阻估算性能评估...")
        validator = EstimationValidator()
        metrics = validator.validate_estimation_results(estimation_results, ground_truth_data)
        
        print(f"内阻估算性能:")
        print(f"  - R0 RMSE: {metrics.r0_rmse*1000:.2f} mΩ")
        print(f"  - R0 MAE: {metrics.r0_mae*1000:.2f} mΩ")
        print(f"  - R0 最大误差: {metrics.r0_max_error*1000:.2f} mΩ")
        print(f"  - R0 相关系数: {metrics.r0_correlation:.4f}")
        
        print(f"电压预测性能:")
        print(f"  - 电压 RMSE: {metrics.voltage_rmse*1000:.1f} mV")
        print(f"  - 电压 MAE: {metrics.voltage_mae*1000:.1f} mV")
        
        # 10. 可视化结果
        print("\n📈 生成内阻估算可视化...")
        visualizer = EstimationVisualizer()
        
        # 估算结果图
        fig1 = visualizer.plot_estimation_results(
            estimation_results,
            ground_truth_data,
            save_path="resistance_estimation_results.png"
        )
        
        # 参数收敛图
        fig2 = visualizer.plot_parameter_convergence(
            estimation_results,
            save_path="resistance_parameter_convergence.png"
        )
        
        # 验证指标图
        fig3 = visualizer.plot_validation_metrics(
            metrics,
            save_path="resistance_validation_metrics.png"
        )
        
        # 创建专门的内阻分析图
        fig4, axes = plt.subplots(2, 2, figsize=(12, 8))
        fig4.suptitle('内阻估算详细分析', fontsize=16, fontweight='bold')
        
        # 提取时间和数据
        times = [(r.timestamp - estimation_results[0].timestamp) / 60.0 for r in estimation_results]
        r0_est = [r.r0 * 1000 for r in estimation_results]
        r1_est = [r.r1 * 1000 for r in estimation_results]
        r0_true = [p['r0_true'] * 1000 for p in parameter_history]
        r0_errors = [abs(e - t) for e, t in zip(r0_est, r0_true)]
        
        # R0估算对比
        axes[0, 0].plot(times, r0_est, 'b-', linewidth=2, label='UKF估算')
        axes[0, 0].plot(times, r0_true, 'r--', linewidth=2, label='真实值')
        axes[0, 0].set_xlabel('时间 (分钟)')
        axes[0, 0].set_ylabel('欧姆内阻 (mΩ)')
        axes[0, 0].set_title('欧姆内阻估算对比')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # R1估算
        axes[0, 1].plot(times, r1_est, 'g-', linewidth=2)
        axes[0, 1].set_xlabel('时间 (分钟)')
        axes[0, 1].set_ylabel('极化内阻 (mΩ)')
        axes[0, 1].set_title('极化内阻估算')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 内阻误差
        axes[1, 0].plot(times, r0_errors, 'purple', linewidth=2)
        axes[1, 0].set_xlabel('时间 (分钟)')
        axes[1, 0].set_ylabel('R0估算误差 (mΩ)')
        axes[1, 0].set_title('欧姆内阻估算误差')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 误差统计
        axes[1, 1].hist(r0_errors, bins=20, alpha=0.7, color='orange')
        axes[1, 1].set_xlabel('R0估算误差 (mΩ)')
        axes[1, 1].set_ylabel('频次')
        axes[1, 1].set_title('误差分布')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig("resistance_detailed_analysis.png", dpi=300, bbox_inches='tight')
        
        print("✅ 图表已保存:")
        print("  - resistance_estimation_results.png")
        print("  - resistance_parameter_convergence.png")
        print("  - resistance_validation_metrics.png")
        print("  - resistance_detailed_analysis.png")
        
        # 11. 生成报告
        print("\n📋 生成内阻估算报告...")
        report = validator.generate_validation_report(metrics)
        
        # 添加内阻专门分析
        resistance_analysis = f"""

=== 内阻估算专门分析 ===

激励信号设计:
- 阶跃响应 (步骤 0-49): 测试动态响应
- 正弦波激励 (步骤 50-99): 频域特性分析  
- 脉冲激励 (步骤 100-149): 瞬态响应测试
- 随机激励 (步骤 150-199): 鲁棒性测试
- 恒流测试 (步骤 200-299): 稳态性能验证

内阻估算统计:
- 平均R0估算值: {np.mean(r0_est):.2f} mΩ
- R0估算标准差: {np.std(r0_est):.2f} mΩ
- 平均R1估算值: {np.mean(r1_est):.2f} mΩ
- R1估算标准差: {np.std(r1_est):.2f} mΩ

误差分析:
- 平均绝对误差: {np.mean(r0_errors):.2f} mΩ
- 误差标准差: {np.std(r0_errors):.2f} mΩ
- 95%置信区间: ±{np.percentile(r0_errors, 95):.2f} mΩ
"""
        
        full_report = report + resistance_analysis
        
        with open("resistance_estimation_report.txt", "w", encoding="utf-8") as f:
            f.write(full_report)
        
        print("✅ 详细报告已保存: resistance_estimation_report.txt")
        
        # 12. 最终总结
        print("\n" + "=" * 60)
        print("🎯 内阻估算示例完成!")
        print(f"⚡ 欧姆内阻估算精度: RMSE = {metrics.r0_rmse*1000:.1f} mΩ")
        print(f"🔋 处理了 {len(estimation_results)} 个数据点")
        
        # 性能评级
        if metrics.r0_rmse < 0.001:
            print("🏆 内阻估算性能: 优秀 (< 1mΩ)")
        elif metrics.r0_rmse < 0.005:
            print("👍 内阻估算性能: 良好 (< 5mΩ)")
        else:
            print("⚠️  内阻估算性能: 需要改进 (≥ 5mΩ)")
        
        plt.show()
        return True
        
    except Exception as e:
        print(f"❌ 内阻估算示例失败: {e}")
        logging.error(f"示例运行失败: {e}", exc_info=True)
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 内阻估算示例运行成功!")
    else:
        print("\n❌ 内阻估算示例运行失败!")
        sys.exit(1)

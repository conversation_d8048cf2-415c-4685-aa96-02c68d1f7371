"""
增强仿真器

基于原有的CellSimulator和BatteryPackSimulator，
增加异常注入功能的增强版仿真器。
"""

import sys
import os
import logging
import numpy as np
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass

# 添加battery_simulator到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'battery_simulator'))

try:
    from cell_simulator import CellSimulator, CellState
    from pack_simulator import BatteryPackSimulator, PackState
    from pack_topology import PackTopology
    BATTERY_SIMULATOR_AVAILABLE = True
except ImportError as e:
    logging.error(f"无法导入battery_simulator模块: {e}")
    BATTERY_SIMULATOR_AVAILABLE = False
    # 创建占位符类
    class CellSimulator:
        pass
    class BatteryPackSimulator:
        pass
    class CellState:
        pass
    class PackState:
        pass
    class PackTopology:
        pass

try:
    from .anomaly_injector import AnomalyInjector
    from .anomaly_types import AnomalyType, ResistanceAnomaly, CapacityAnomaly, ShortCircuitAnomaly, ThermalAnomaly
except ImportError:
    from anomaly_injector import AnomalyInjector
    from anomaly_types import AnomalyType, ResistanceAnomaly, CapacityAnomaly, ShortCircuitAnomaly, ThermalAnomaly


class EnhancedCellSimulator(CellSimulator):
    """
    增强电芯仿真器
    
    在原有CellSimulator基础上增加异常注入功能。
    支持通过修改PyBaMM参数和备用模型参数来实现物理异常注入。
    """
    
    def __init__(self, cell_id: int, chemistry: str = "LiFePO4", 
                 model_type: str = "SPM", include_thermal: bool = True):
        """
        初始化增强电芯仿真器
        
        Args:
            cell_id: 电芯唯一标识
            chemistry: 电池化学体系
            model_type: 模型类型
            include_thermal: 是否包含热效应
        """
        if not BATTERY_SIMULATOR_AVAILABLE:
            raise RuntimeError("battery_simulator模块不可用，无法创建增强仿真器")
        
        # 调用父类初始化
        super().__init__(cell_id, chemistry, model_type, include_thermal)
        
        # 异常注入相关
        self.active_anomalies: Dict[str, AnomalyType] = {}  # event_id -> anomaly_type
        self.original_parameters: Dict[str, Any] = {}       # 保存原始参数
        self.parameter_modifications: Dict[str, Any] = {}   # 当前参数修改
        
        # 备用模型修改
        self.fallback_modifications: Dict[str, Any] = {}
        
        # 短路特殊处理
        self.is_short_circuited = False
        self.leakage_current = 0.0
        self.additional_heat_generation = 0.0
        
        logging.debug(f"增强电芯仿真器初始化: 电芯{cell_id}")
    
    def inject_anomaly(self, event_id: str, anomaly_type: AnomalyType, current_time: float) -> bool:
        """
        注入异常
        
        Args:
            event_id: 事件ID
            anomaly_type: 异常类型
            current_time: 当前时间
            
        Returns:
            bool: 注入是否成功
        """
        try:
            logging.info(f"电芯{self.cell_id}: 注入异常 {anomaly_type.name} (事件ID: {event_id})")
            
            # 更新异常类型的当前时间（用于计算渐变效果）
            anomaly_type.onset_time = current_time
            
            # 保存到活跃异常列表
            self.active_anomalies[event_id] = anomaly_type
            
            # 根据异常类型执行不同的注入策略
            if isinstance(anomaly_type, ResistanceAnomaly):
                return self._inject_resistance_anomaly(anomaly_type, current_time)
            elif isinstance(anomaly_type, CapacityAnomaly):
                return self._inject_capacity_anomaly(anomaly_type, current_time)
            elif isinstance(anomaly_type, ShortCircuitAnomaly):
                return self._inject_short_circuit_anomaly(anomaly_type, current_time)
            elif isinstance(anomaly_type, ThermalAnomaly):
                return self._inject_thermal_anomaly(anomaly_type, current_time)
            else:
                logging.warning(f"未知异常类型: {type(anomaly_type)}")
                return False
                
        except Exception as e:
            logging.error(f"电芯{self.cell_id}: 异常注入失败: {e}")
            return False
    
    def remove_anomaly(self, event_id: str) -> bool:
        """
        移除异常
        
        Args:
            event_id: 事件ID
            
        Returns:
            bool: 移除是否成功
        """
        try:
            if event_id not in self.active_anomalies:
                logging.warning(f"电芯{self.cell_id}: 尝试移除不存在的异常事件 {event_id}")
                return False
            
            anomaly_type = self.active_anomalies[event_id]
            logging.info(f"电芯{self.cell_id}: 移除异常 {anomaly_type.name} (事件ID: {event_id})")
            
            # 从活跃异常列表中移除
            del self.active_anomalies[event_id]
            
            # 重新计算所有剩余异常的综合效果
            self._recalculate_all_anomaly_effects()
            
            return True
            
        except Exception as e:
            logging.error(f"电芯{self.cell_id}: 异常移除失败: {e}")
            return False
    
    def simulate_step(self, current: float, ambient_temperature: float, dt: float) -> CellState:
        """
        重写仿真步骤，加入异常处理
        
        Args:
            current: 电流 (A)
            ambient_temperature: 环境温度 (°C)
            dt: 时间步长 (s)
            
        Returns:
            CellState: 电芯状态
        """
        # 1. 更新所有异常的时间相关效果
        self._update_anomaly_effects()
        
        # 2. 处理短路异常的电流修改
        effective_current = self._apply_short_circuit_current_modification(current)
        
        # 3. 调用父类的仿真步骤
        cell_state = super().simulate_step(effective_current, ambient_temperature, dt)
        
        # 4. 应用异常对结果的后处理
        cell_state = self._apply_anomaly_post_processing(cell_state, current, dt)
        
        return cell_state
    
    def _inject_resistance_anomaly(self, anomaly: ResistanceAnomaly, current_time: float) -> bool:
        """注入内阻异常"""
        try:
            # 获取PyBaMM参数修改
            pybamm_changes = anomaly.get_pybamm_parameter_changes()

            # 如果有PyBaMM仿真对象，修改其参数
            if hasattr(self, 'parameter_values') and self.parameter_values is not None:
                for param_name, new_value in pybamm_changes.items():
                    # 保存原始值（如果还没保存）
                    if param_name not in self.original_parameters:
                        try:
                            self.original_parameters[param_name] = self.parameter_values[param_name]
                        except:
                            self.original_parameters[param_name] = None

                    # 应用新值
                    self.parameter_values.update({param_name: new_value})
                    logging.debug(f"电芯{self.cell_id}: 更新PyBaMM参数 {param_name} = {new_value}")

                # 重新初始化PyBaMM仿真以使参数生效
                self._reinitialize_pybamm_simulation()

                # 关键修复：清除累积实验历史，避免参数修改冲突
                if hasattr(self, 'simulation') and self.simulation:
                    if hasattr(self.simulation, '_experiment_history'):
                        self.simulation._experiment_history = []
                        self.simulation._total_time = 0.0
                        logging.debug(f"电芯{self.cell_id}: 清除累积实验历史以避免参数冲突")

            # 获取备用模型修改
            fallback_changes = anomaly.get_fallback_modifications()
            self.fallback_modifications.update(fallback_changes)

            return True

        except Exception as e:
            logging.error(f"电芯{self.cell_id}: 内阻异常注入失败: {e}")
            return False
    
    def _inject_capacity_anomaly(self, anomaly: CapacityAnomaly, current_time: float) -> bool:
        """注入容量异常"""
        try:
            # 获取PyBaMM参数修改
            pybamm_changes = anomaly.get_pybamm_parameter_changes()
            
            # 修改PyBaMM参数
            if hasattr(self, 'parameter_values') and self.parameter_values is not None:
                logging.debug(f"电芯{self.cell_id}: 开始应用容量异常参数修改")
                logging.debug(f"电芯{self.cell_id}: pybamm_changes = {pybamm_changes}")

                for param_name, target_value in pybamm_changes.items():
                    logging.debug(f"电芯{self.cell_id}: 处理参数 {param_name}, 目标值 = {target_value}")

                    # 保存原始值
                    if param_name not in self.original_parameters:
                        try:
                            original_value = self.parameter_values[param_name]
                            self.original_parameters[param_name] = original_value
                            logging.debug(f"电芯{self.cell_id}: 保存原始值 {param_name} = {original_value}")
                        except:
                            self.original_parameters[param_name] = None
                            logging.debug(f"电芯{self.cell_id}: 无法获取原始值 {param_name}")

                    # 对于容量异常，直接使用目标值（绝对值）
                    new_value = target_value
                    logging.debug(f"电芯{self.cell_id}: 容量参数{param_name}直接使用目标值: {new_value}")

                    # 应用新值
                    self.parameter_values.update({param_name: new_value})
                    logging.debug(f"电芯{self.cell_id}: 更新PyBaMM参数 {param_name} = {new_value}")

                    # 验证更新是否成功
                    actual_value = self.parameter_values[param_name]
                    logging.debug(f"电芯{self.cell_id}: 验证更新后的值 {param_name} = {actual_value}")

                    if abs(float(actual_value) - float(new_value)) > 1e-9:
                        logging.error(f"电芯{self.cell_id}: 参数更新失败！期望{new_value}, 实际{actual_value}")
                    else:
                        logging.debug(f"电芯{self.cell_id}: 参数更新成功 ✅")

                # 关键修复：容量异常需要重新设置合适的初始条件
                # 容量异常的参数修改与当前SOC状态不兼容，需要重新开始

                # 重新设置为安全的初始SOC（0.5）
                safe_soc = 0.5
                current_temp = self.current_state.temperature if self.current_state else 25.0

                try:
                    # 只重新创建仿真对象，不重新初始化整个模型（避免参数被覆盖）
                    if hasattr(self, 'pybamm_adapter') and hasattr(self, 'model'):
                        # 使用修改后的参数创建新的仿真对象
                        self.simulation = self.pybamm_adapter.create_simulation(
                            self.model,
                            self.parameter_values
                        )
                        logging.debug(f"电芯{self.cell_id}: 使用修改后参数重新创建仿真对象")

                    # 设置安全的初始条件
                    self.set_initial_conditions(
                        initial_soc=safe_soc,
                        initial_temperature=current_temp
                    )

                    # 容量异常不需要特殊模式，使用正常的累积实验
                    # 清除累积历史，从新的参数开始
                    if hasattr(self, 'simulation') and self.simulation:
                        if hasattr(self.simulation, '_experiment_history'):
                            self.simulation._experiment_history = []
                            self.simulation._total_time = 0.0
                            logging.debug(f"电芯{self.cell_id}: 清除累积实验历史，使用新参数重新开始")

                    logging.debug(f"电芯{self.cell_id}: 容量异常注入成功，重新设置初始条件 SOC={safe_soc}")

                except Exception as e:
                    logging.error(f"电芯{self.cell_id}: 容量异常重新初始化失败: {e}")
                    return False

            # 获取备用模型修改
            fallback_changes = anomaly.get_fallback_modifications()
            self.fallback_modifications.update(fallback_changes)
            
            return True
            
        except Exception as e:
            logging.error(f"电芯{self.cell_id}: 容量异常注入失败: {e}")
            return False
    
    def _inject_short_circuit_anomaly(self, anomaly: ShortCircuitAnomaly, current_time: float) -> bool:
        """注入短路异常"""
        try:
            # 短路异常主要通过修改电流和增加产热来实现
            fallback_changes = anomaly.get_fallback_modifications()
            
            self.is_short_circuited = True
            self.leakage_current = fallback_changes.get('leakage_current', 0.0)
            self.additional_heat_generation = fallback_changes.get('additional_heat_generation', 0.0)
            
            logging.debug(f"电芯{self.cell_id}: 短路异常激活，漏电流={self.leakage_current}A")
            
            return True
            
        except Exception as e:
            logging.error(f"电芯{self.cell_id}: 短路异常注入失败: {e}")
            return False
    
    def _inject_thermal_anomaly(self, anomaly: ThermalAnomaly, current_time: float) -> bool:
        """注入热异常"""
        try:
            # 热异常通过修改PyBaMM热参数来实现
            pybamm_changes = anomaly.get_pybamm_parameter_changes()

            # 修改PyBaMM参数
            if hasattr(self, 'parameter_values') and self.parameter_values is not None:
                for param_name, new_value in pybamm_changes.items():
                    # 保存原始值
                    if param_name not in self.original_parameters:
                        try:
                            self.original_parameters[param_name] = self.parameter_values[param_name]
                        except:
                            self.original_parameters[param_name] = None

                    # 应用新值（热参数都是绝对值，不需要乘数计算）
                    self.parameter_values.update({param_name: new_value})
                    logging.debug(f"电芯{self.cell_id}: 更新热参数 {param_name} = {new_value}")

            # 重新创建仿真对象以应用新的热参数
            if hasattr(self, 'pybamm_adapter') and hasattr(self, 'model'):
                self.simulation = self.pybamm_adapter.create_simulation(
                    self.model,
                    self.parameter_values
                )
                logging.debug(f"电芯{self.cell_id}: 使用修改后热参数重新创建仿真对象")

            logging.debug(f"电芯{self.cell_id}: 热异常激活")

            return True

        except Exception as e:
            logging.error(f"电芯{self.cell_id}: 热异常注入失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _reinitialize_pybamm_simulation(self):
        """重新初始化PyBaMM仿真以使参数修改生效"""
        try:
            if hasattr(self, 'pybamm_adapter') and self.pybamm_adapter:
                # 保存当前状态
                current_soc = self.current_state.soc if self.current_state else 0.5
                current_temp = self.current_state.temperature if self.current_state else 25.0

                # 重新创建仿真（修复API调用）
                model = self.pybamm_adapter.create_model(self.chemistry, include_thermal=self.include_thermal)
                self.simulation = self.pybamm_adapter.create_simulation(model, self.parameter_values)

                # 重新设置初始条件
                if self.simulation:
                    self.pybamm_adapter.set_initial_conditions(
                        self.simulation,
                        initial_soc=current_soc,
                        initial_temperature=current_temp
                    )
                    logging.debug(f"电芯{self.cell_id}: PyBaMM仿真重新初始化成功")
                    return True
                else:
                    logging.warning(f"电芯{self.cell_id}: PyBaMM仿真重新创建失败")
                    return False
            else:
                logging.warning(f"电芯{self.cell_id}: 无PyBaMM适配器，无法重新初始化")
                return False

        except Exception as e:
            logging.error(f"电芯{self.cell_id}: PyBaMM仿真重新初始化失败: {e}")
            return False

    def _update_anomaly_effects(self) -> None:
        """更新所有异常的时间相关效果"""
        current_time = self.simulation_time

        for event_id, anomaly in self.active_anomalies.items():
            # 计算当前严重程度因子
            severity_factor = anomaly.calculate_current_severity(current_time)

            # 根据严重程度因子更新参数
            # 注意：PyBaMM参数已经在异常注入时设置，这里只处理其他类型的异常
            if isinstance(anomaly, ShortCircuitAnomaly):
                self._update_short_circuit_with_factor(anomaly, severity_factor)
            elif isinstance(anomaly, ThermalAnomaly):
                self._update_thermal_with_factor(anomaly, severity_factor)
            # ResistanceAnomaly和CapacityAnomaly的参数已经在注入时设置



    def _update_short_circuit_with_factor(self, anomaly: ShortCircuitAnomaly, factor: float) -> None:
        """根据严重程度因子更新短路效果"""
        base_leakage = anomaly.parameters['leakage_current']
        self.leakage_current = base_leakage * factor
        self.additional_heat_generation = self.leakage_current * 0.1  # 简化的产热计算

    def _update_thermal_with_factor(self, anomaly: ThermalAnomaly, factor: float) -> None:
        """根据严重程度因子更新热异常效果"""
        base_thermal_mult = anomaly.parameters['thermal_resistance_multiplier']
        base_heat_source = anomaly.parameters['additional_heat_source']

        current_thermal_mult = 1.0 + (base_thermal_mult - 1.0) * factor
        current_heat_source = base_heat_source * factor

        self.fallback_modifications.update({
            'thermal_resistance_multiplier': current_thermal_mult,
            'additional_heat_generation': current_heat_source
        })

    def _apply_short_circuit_current_modification(self, current: float) -> float:
        """应用短路异常的电流修改"""
        if self.is_short_circuited and self.leakage_current > 0:
            # 从外部电流中减去漏电流
            effective_current = current - self.leakage_current
            logging.debug(f"电芯{self.cell_id}: 短路电流修改 {current}A -> {effective_current}A (漏电流{self.leakage_current}A)")
            return effective_current
        return current

    def _apply_anomaly_post_processing(self, cell_state: CellState, original_current: float, dt: float) -> CellState:
        """对仿真结果应用异常后处理"""
        # 应用备用模型修改
        if self.fallback_modifications:
            cell_state = self._apply_fallback_modifications(cell_state, original_current, dt)

        # 应用额外产热
        if self.additional_heat_generation > 0:
            # 简化的温度增加计算
            thermal_mass = 500.0  # J/K，简化值
            temp_increase = self.additional_heat_generation * dt / thermal_mass
            cell_state.temperature += temp_increase

        return cell_state

    def _apply_fallback_modifications(self, cell_state: CellState, current: float, dt: float) -> CellState:
        """应用备用模型修改"""
        # 内阻修改
        if 'base_resistance_multiplier' in self.fallback_modifications:
            multiplier = self.fallback_modifications['base_resistance_multiplier']
            if cell_state.internal_resistance is not None:
                cell_state.internal_resistance *= multiplier

        # 容量修改
        if 'nominal_capacity_multiplier' in self.fallback_modifications:
            multiplier = self.fallback_modifications['nominal_capacity_multiplier']
            # 这里可能需要重新计算SOC，但为了简化暂时跳过

        # 热阻修改
        if 'thermal_resistance_multiplier' in self.fallback_modifications:
            # 这会影响温度计算，但需要更复杂的热模型
            pass

        return cell_state

    def _recalculate_all_anomaly_effects(self) -> None:
        """重新计算所有剩余异常的综合效果"""
        # 重置修改
        self.parameter_modifications.clear()
        self.fallback_modifications.clear()
        self.is_short_circuited = False
        self.leakage_current = 0.0
        self.additional_heat_generation = 0.0

        # 重新应用所有活跃异常
        current_time = self.simulation_time
        for event_id, anomaly in self.active_anomalies.items():
            if isinstance(anomaly, ResistanceAnomaly):
                self._inject_resistance_anomaly(anomaly, current_time)
            elif isinstance(anomaly, CapacityAnomaly):
                self._inject_capacity_anomaly(anomaly, current_time)
            elif isinstance(anomaly, ShortCircuitAnomaly):
                self._inject_short_circuit_anomaly(anomaly, current_time)
            elif isinstance(anomaly, ThermalAnomaly):
                self._inject_thermal_anomaly(anomaly, current_time)

    def get_anomaly_status(self) -> Dict[str, Any]:
        """获取异常状态信息"""
        return {
            'cell_id': self.cell_id,
            'active_anomalies': len(self.active_anomalies),
            'anomaly_details': [
                {
                    'event_id': event_id,
                    'anomaly_type': anomaly.name,
                    'severity': anomaly.severity.value,
                    'description': anomaly.description
                }
                for event_id, anomaly in self.active_anomalies.items()
            ],
            'is_short_circuited': self.is_short_circuited,
            'leakage_current': self.leakage_current,
            'additional_heat_generation': self.additional_heat_generation,
            'parameter_modifications': dict(self.parameter_modifications),
            'fallback_modifications': dict(self.fallback_modifications)
        }


class EnhancedPackSimulator(BatteryPackSimulator):
    """
    增强电池包仿真器

    在原有BatteryPackSimulator基础上集成异常注入功能。
    """

    def __init__(self, topology: PackTopology, chemistry: str = "LiFePO4",
                 model_type: str = "SPM", include_thermal: bool = True,
                 anomaly_injector: Optional[AnomalyInjector] = None):
        """
        初始化增强电池包仿真器

        Args:
            topology: 电池包拓扑结构
            chemistry: 电池化学体系
            model_type: PyBaMM模型类型
            include_thermal: 是否包含热效应
            anomaly_injector: 异常注入器 (可选)
        """
        if not BATTERY_SIMULATOR_AVAILABLE:
            raise RuntimeError("battery_simulator模块不可用，无法创建增强仿真器")

        # 不调用父类初始化，而是手动创建组件
        self.topology = topology
        self.chemistry = chemistry
        self.model_type = model_type
        self.include_thermal = include_thermal

        # 创建增强电芯仿真器
        self.cell_simulators: List[EnhancedCellSimulator] = []
        for cell_id in range(topology.total_cells):
            simulator = EnhancedCellSimulator(
                cell_id=cell_id,
                chemistry=chemistry,
                model_type=model_type,
                include_thermal=include_thermal
            )
            self.cell_simulators.append(simulator)

        # 创建包级模型 (从父类复制)
        if include_thermal:
            from pack_simulator import PackThermalModel
            self.thermal_model = PackThermalModel(topology)
        else:
            self.thermal_model = None

        from pack_simulator import PackElectricalModel
        self.electrical_model = PackElectricalModel(topology)

        # 创建BMS系统
        from bms import BatteryManagementSystem
        self.bms = BatteryManagementSystem(chemistry=chemistry, cell_count=topology.total_cells)

        # 仿真状态
        self.current_pack_state: Optional[PackState] = None
        self.simulation_time = 0.0
        self.is_initialized = False

        # 性能优化参数 (从父类复制)
        self._state_cache = {}
        self._cache_valid = False
        self._last_update_time = 0.0
        self._cache_timeout = 0.1
        self._batch_mode = False
        self._fast_mode = False

        # 异常注入器
        self.anomaly_injector = anomaly_injector

        logging.info(f"增强电池包仿真器初始化: {topology.pack_name}, "
                    f"{topology.total_cells}个{chemistry}电芯, "
                    f"异常注入器: {'已配置' if anomaly_injector else '未配置'}")

    def simulate_pack_step(self, pack_current: float, ambient_temperature: float,
                          dt: float) -> PackState:
        """
        仿真电池包一个时间步 (集成异常注入)

        Args:
            pack_current: 包电流 (A, 正为充电)
            ambient_temperature: 环境温度 (°C)
            dt: 时间步长 (s)

        Returns:
            新的包状态 (包含异常注入效果)
        """
        if not self.is_initialized:
            raise RuntimeError("增强电池包仿真器未初始化")

        # 1. 异常注入更新
        injection_results = []
        if self.anomaly_injector:
            system_state = self._get_system_state_for_injector()
            injection_results = self.anomaly_injector.update(self.simulation_time, system_state)

            # 应用异常注入到相应的电芯
            for result in injection_results:
                if result.success and 0 <= result.cell_id < len(self.cell_simulators):
                    enhanced_cell = self.cell_simulators[result.cell_id]
                    if result.status.value == 'active':
                        # 激活异常
                        event = self.anomaly_injector.active_injections.get(result.event_id)
                        if event:
                            enhanced_cell.inject_anomaly(result.event_id, event.anomaly_type, self.simulation_time)
                    elif result.status.value == 'completed':
                        # 移除异常
                        enhanced_cell.remove_anomaly(result.event_id)

        # 2. 调用父类的仿真步骤逻辑 (手动实现)
        pack_state = self._simulate_pack_step_with_anomalies(pack_current, ambient_temperature, dt)

        # 3. 在包状态中添加异常信息
        if hasattr(pack_state, '__dict__'):
            pack_state.__dict__['injection_results'] = injection_results
            pack_state.__dict__['active_anomalies'] = self.get_active_anomalies_summary()

        return pack_state

    def _get_system_state_for_injector(self) -> Dict[str, Any]:
        """获取系统状态用于异常注入器的条件判断"""
        if self.current_pack_state is None:
            return {}

        return {
            'pack_voltage': self.current_pack_state.pack_voltage,
            'pack_current': self.current_pack_state.pack_current,
            'pack_soc': self.current_pack_state.pack_soc,
            'pack_temperature': self.current_pack_state.pack_temperature,
            'max_cell_temperature': self.current_pack_state.max_cell_temperature,
            'min_cell_temperature': self.current_pack_state.min_cell_temperature
        }

    def _simulate_pack_step_with_anomalies(self, pack_current: float,
                                          ambient_temperature: float, dt: float):
        """带异常注入的包仿真步骤"""
        # 这里应该调用父类的simulate_pack_step方法
        # 但由于我们没有完全继承，暂时返回一个模拟的包状态
        from pack_simulator import PackState
        from cell_simulator import CellState

        # 创建模拟的电芯状态
        cell_states = []
        for i in range(len(self.cell_simulators)):
            cell_state = CellState(
                cell_id=i,
                voltage=3.2 + 0.01 * i,  # 模拟电压
                current=pack_current / len(self.cell_simulators),
                soc=0.5,
                temperature=25.0,
                heat_generation_rate=0.1,
                internal_resistance=0.003,
                ocv=3.3,
                polarization_voltage=0.1,
                simulation_quality=1.0,
                is_valid=True
            )
            cell_states.append(cell_state)

        # 创建模拟的包状态
        pack_state = PackState(
            pack_voltage=sum(cs.voltage for cs in cell_states),
            pack_current=pack_current,
            pack_soc=0.5,
            pack_power=pack_current * sum(cs.voltage for cs in cell_states),
            pack_temperature=25.0,
            max_cell_temperature=25.0,
            min_cell_temperature=25.0,
            temperature_difference=0.0,
            cell_voltage_std=0.01,
            cell_temperature_std=0.1,
            cell_states=cell_states,
            bms_status=None,
            bms_protection_active=False,
            actual_current=pack_current,
            simulation_quality=1.0,
            is_valid=True
        )

        self.current_pack_state = pack_state
        self.simulation_time += dt

        return pack_state

    def get_active_anomalies_summary(self) -> Dict[str, Any]:
        """获取活跃异常摘要"""
        if not self.anomaly_injector:
            return {}

        active_anomalies = self.anomaly_injector.get_active_anomalies()
        summary = {}

        for cell_id, events in active_anomalies.items():
            summary[f'cell_{cell_id}'] = [
                {
                    'event_id': event.event_id,
                    'anomaly_type': event.anomaly_type.name,
                    'severity': event.anomaly_type.severity.value,
                    'description': event.description
                }
                for event in events
            ]

        return summary
